<?php

use Stripe\CountrySpec;
use Xmetr\Base\Facades\Assets;
use Xmetr\Page\Forms\PageForm;
use Xmetr\Theme\Facades\Theme;
use Xmetr\Base\Facades\MetaBox;
use Xmetr\Location\Models\City;
use Xmetr\Base\Models\BaseModel;
use Xmetr\Media\Facades\RvMedia;
use Xmetr\Media\Models\MediaFile;
use Illuminate\Support\Facades\DB;
use Xmetr\Base\Forms\FormAbstract;
use Xmetr\Location\Models\Country;
use Xmetr\Slug\Facades\SlugHelper;
use Xmetr\Base\Facades\AdminHelper;
use Xmetr\Base\Enums\BaseStatusEnum;
use Xmetr\Base\Rules\MediaImageRule;
use Xmetr\RealEstate\Models\Feature;
use Xmetr\RealEstate\Models\Project;
use Xmetr\Base\Facades\DashboardMenu;
use Xmetr\RealEstate\Models\Category;
use Xmetr\RealEstate\Models\Property;
use Xmetr\Widget\Facades\WidgetGroup;
use Xmetr\Base\Forms\Fields\HtmlField;
use Xmetr\Base\Forms\Fields\TextField;
use Xmetr\SeoHelper\Facades\SeoHelper;
use Xmetr\Theme\Supports\ThemeSupport;
use Xmetr\Base\Forms\Fields\ColorField;
use Xmetr\Base\Forms\Fields\OnOffField;
use Xmetr\RealEstate\Forms\AccountForm;
use Xmetr\RealEstate\Forms\FeatureForm;
use Xmetr\RealEstate\Forms\ProjectForm;
use Xmetr\Base\Forms\Fields\HiddenField;
use Xmetr\Base\Forms\Fields\NumberField;
use Xmetr\Base\Forms\Fields\SelectField;
use Xmetr\Newsletter\Facades\Newsletter;
use Xmetr\RealEstate\Forms\CategoryForm;
use Xmetr\RealEstate\Forms\PropertyForm;
use Xmetr\Support\Http\Requests\Request;
use Xmetr\Base\Forms\Fields\CoreIconField;
use Xmetr\Base\Forms\Fields\RepeaterField;
use Xmetr\Base\Forms\Fields\TextareaField;
use Xmetr\Theme\Typography\TypographyItem;
use Illuminate\Routing\Events\RouteMatched;
use Xmetr\Contact\Forms\Fronts\ContactForm;
use Xmetr\Base\Forms\Fields\MediaImageField;
use Xmetr\RealEstate\Enums\PropertyTypeEnum;
use Xmetr\Base\Forms\Fields\PhoneNumberField;
use Xmetr\RealEstate\Enums\ProjectStatusEnum;
use Xmetr\RealEstate\Enums\PropertyPeriodEnum;
use Xmetr\RealEstate\Enums\PropertyStatusEnum;
use Xmetr\RealEstate\Facades\RealEstateHelper;
use Xmetr\RealEstate\Forms\Fronts\ProfileForm;
use Xmetr\Base\Forms\Fields\OnOffCheckboxField;
use Xmetr\RealEstate\Forms\AccountPropertyForm;
use Xmetr\RealEstate\Forms\Fronts\Auth\LoginForm;
use Xmetr\Base\Forms\FieldOptions\TextFieldOption;
use Xmetr\RealEstate\Http\Requests\SettingRequest;
use Xmetr\Base\Forms\FieldOptions\ColorFieldOption;
use Xmetr\Base\Forms\FieldOptions\OnOffFieldOption;
use Xmetr\Base\Forms\FieldOptions\HiddenFieldOption;
use Xmetr\Base\Forms\FieldOptions\NumberFieldOption;
use Xmetr\Base\Forms\FieldOptions\SelectFieldOption;
use Xmetr\RealEstate\Forms\Fronts\Auth\RegisterForm;
use Xmetr\Base\Forms\FieldOptions\CoreIconFieldOption;
use Xmetr\Base\Forms\FieldOptions\RepeaterFieldOption;
use Xmetr\Base\Forms\FieldOptions\TextareaFieldOption;
use Xmetr\RealEstate\Http\Requests\AccountEditRequest;
use Theme\Xmetr\Forms\Fields\GoogleMapAutocompleteField;
use Xmetr\Base\Forms\FieldOptions\MediaImageFieldOption;
use Xmetr\RealEstate\Http\Requests\AccountCreateRequest;
use Xmetr\Location\Repositories\Interfaces\CityInterface;
use Xmetr\RealEstate\Http\Requests\AccountPropertyRequest;
use Xmetr\Location\Repositories\Interfaces\CountryInterface;
use Xmetr\Base\Supports\DashboardMenu as DashboardMenuSupport;
use Xmetr\RealEstate\Http\Requests\Fronts\Auth\RegisterRequest;

if (! function_exists('get_max_properties_price')) {
    function get_max_properties_price(): int
    {
        $price = Property::query()->max('price');

        return $price ? (int) ceil($price) : 0;
    }
}

if (! function_exists('get_min_square')) {
    function get_min_square(): int
    {
        $square = Property::query()->min('square');

        return $square ? (int) ceil($square) : 0;
    }
}

if (! function_exists('get_max_square')) {
    function get_max_square(): int
    {
        $square = Property::query()->max('square');

        return $square ? (int) ceil($square) : 0;
    }
}

if (! function_exists('get_min_flat')) {
    function get_min_flat(): int
    {
        $flat = Project::query()->min('number_flat');

        return $flat ? (int) ceil($flat) : 0;
    }
}

if (! function_exists('get_max_flat')) {
    function get_max_flat(): int
    {
        $flat = Project::query()->max('number_flat');

        return $flat ? (int) ceil($flat) : 0;
    }
}

if (! function_exists('get_property_listing_page_layout')) {
    function get_property_listing_page_layout(string $default = 'top-map'): string
    {
        $layout = theme_option('real_estate_property_listing_layout', $default);

        return in_array($layout, ['top-map', 'half-map', 'sidebar']) ? $layout : $default;
    }
}


if (! function_exists('get_all_countries')) {
    function get_all_countries()
    {
        // $condition = ['status' => BaseStatusEnum::PUBLISHED];

        // $countries = app(CountryInterface::class)->advancedGet([
        //     'condition' => $condition,
        //     'with' => 'properties',
        //     'order_by' => ['name' => 'asc']
        // ]);

        // $countries = DB::table('countries')
        //     ->join('re_properties', 'countries.id', '=', 're_properties.country_id')
        //     ->select('countries.*')
        //     ->distinct()
        //     ->get();

        $countries = Country::query()
            ->with('slugable')
            ->whereHas('properties')
            ->orderBy('name')
            ->get();

        return $countries;
    }
}

if (! function_exists('get_all_cities')) {
    function get_all_cities($country_id = null)
    {
        // $condition = ['status' => BaseStatusEnum::PUBLISHED];

        // $cities = app(CityInterface::class)->advancedGet([
        //     'condition' => $condition,
        //     'order_by' => ['name' => 'asc']
        // ]);

        // $query = DB::table('cities')
        //     ->join('re_properties', 'cities.id', '=', 're_properties.city_id')
        //     ->select('cities.*')
        //     ->orderBy('order')
        //     ->orderBy('name')
        //     ->distinct();
        // if(!empty($country_id)) {
        //     $query->where('cities.country_id', $country_id);
        // }
        // $cities = $query->get();

        $cities = City::query()
            ->select(['cities.*'])
            ->join('re_properties', 'cities.id', '=', 're_properties.city_id')
            ->when($country_id, function ($query) use ($country_id) {
                $query->where('re_properties.country_id', $country_id);
            })
            ->orderBy('cities.name')
            ->distinct()
            ->get();


        return $cities;
    }
}

if (! function_exists('get_category_name_by_id')) {
    function get_category_name_by_id(int $id): ?string
    {
        // $category = Category::query()
        //     ->select('name')
        //     ->where('id', $id)
        //     ->first();
        $category = Category::findOrFail($id);

        return $category ? $category->name : null;
    }
}
if (! function_exists('get_country_name_by_id')) {
    function get_country_name_by_id(int $id): ?string
    {
        $country = Country::findOrFail($id);

        return $country ? $country->name : null;
    }
}

if (! function_exists('get_city_name_by_id')) {
    function get_city_name_by_id(int $id): ?string
    {
        $city = City::findOrFail($id);

        return $city ? $city->name : null;
    }
}
if (! function_exists('get_country_name_by_id_of_city')) {
    function get_country_name_by_id_of_city(int $id): ?string
    {
        $city = City::query()
            ->where('id', $id)
            ->first();

        return $city ? $city->country->name : null;
    }
}



app()->booted(function (): void {
    register_page_template([
        'default' => __('Default'),
        'full-width' => __('Full Width'),
        'no-layout' => __('No Layout'),
    ]);

    register_sidebar([
        'id' => 'top_footer_sidebar',
        'name' => __('Top Footer Sidebar'),
        'description' => __('Top section of the footer for logo and social links.'),
    ]);

    register_sidebar([
        'id' => 'inner_footer_sidebar',
        'name' => __('Inner Footer Sidebar'),
        'description' => __('Inner footer section for site info, menus, and newsletter.'),
    ]);

    register_sidebar([
        'id' => 'bottom_footer_sidebar',
        'name' => __('Bottom Footer Sidebar'),
        'description' => __('Bottom footer section for legal notices and credits.'),
    ]);

    register_sidebar([
        'id' => 'blog_sidebar',
        'name' => __('Blog Sidebar'),
        'description' => __('Add widgets here to appear in the sidebar of your blog pages.'),
    ]);

    register_sidebar([
        'id' => 'bottom_post_detail_sidebar',
        'name' => __('Bottom Post Detail Sidebar'),
        'description' => __('Place widgets here to display additional content below individual blog posts.'),
    ]);

    WidgetGroup::removeGroup('primary_sidebar');

    Theme::typography()
        ->registerFontFamilies([
            new TypographyItem('primary', __('Primary'), 'DM Sans'),
            new TypographyItem('heading', __('Heading'), 'Josefin Sans'),
        ])
        ->registerFontSizes([
            new TypographyItem('h1', __('Heading 1'), 80),
            new TypographyItem('h2', __('Heading 2'), 56),
            new TypographyItem('h3', __('Heading 3'), 44),
            new TypographyItem('h4', __('Heading 4'), 36),
            new TypographyItem('h5', __('Heading 5'), 30),
            new TypographyItem('h6', __('Heading 6'), 24),
            new TypographyItem('body', __('Body'), 16),
        ]);

    ThemeSupport::registerSocialLinks();
    ThemeSupport::registerSocialSharing();
    ThemeSupport::registerToastNotification();
    ThemeSupport::registerPreloader();
    ThemeSupport::registerSiteCopyright();
    ThemeSupport::registerDateFormatOption();
    ThemeSupport::registerLazyLoadImages();
    ThemeSupport::registerSiteLogoHeight(44);

    if (is_plugin_active('newsletter')) {
        Newsletter::registerNewsletterPopup();
    }

    add_filter('ads_locations', function (array $locations) {
        return [
            ...$locations,
            'header_before' => __('Header (before)'),
            'header_after' => __('Header (after)'),
            'footer_before' => __('Footer (before)'),
            'footer_after' => __('Footer (after)'),
            'listing_page_before' => __('Listing Page (before)'),
            'listing_page_after' => __('Listing Page (after)'),
            'detail_page_before' => __('Detail Page (before)'),
            'detail_page_after' => __('Detail Page (after)'),
            'detail_page_sidebar_before' => __('Detail Page Sidebar (before)'),
            'detail_page_sidebar_after' => __('Detail Page Sidebar (after)'),
            'blog_list_before' => __('Blog List (before)'),
            'blog_list_after' => __('Blog List (after)'),
            'blog_sidebar_before' => __('Blog Sidebar (before)'),
            'blog_sidebar_after' => __('Blog Sidebar (after)'),
            'post_detail_before' => __('Post Detail (before)'),
            'post_detail_after' => __('Post Detail (after)'),
        ];
    }, 128);


    add_filter('account_dashboard_header', function () {
        $version = get_cms_version() . '.4';

        $styles = [
            'css/bootstrap.min.css?v='.$version,
            'css/animate.min.css?v='.$version,
            'plugins/swiper/swiper-bundle.min.css?v='.$version,
            'css/jquery-ui.min.css?v='.$version,
            'css/ace-responsive-menu.css?v='.$version,
            'css/menu.css?v='.$version,
            'css/fontawesome.css?v='.$version,
            'css/flaticon.css?v='.$version,
            'css/bootstrap-select.min.css?v='.$version,
            'css/slider.css?v='.$version,
            'css/ud-custom-spacing.css?v='.$version,
            'css/x-metr.css?v='.$version,
            'css/style.css?v='.$version,
            'css/main.css?v='.$version,
            'css/responsive.css?v='.$version,
            'plugins/slick.css?v='.$version,
            'plugins/slick-theme.css?v='.$version,
            ];

        foreach ($styles as $style) {
            echo '<link media="all" type="text/css" rel="stylesheet" href="' . Theme::asset()->url($style) . '">' . PHP_EOL;
        }
    });

    add_filter('real_estate_dashboard_footer', function () {

        $scripts = [
            // 'js/jquery-3.6.4.min.js',
            // 'js/jquery-migrate-3.0.0.min.js',
            // 'js/popper.min.js',
            // 'js/bootstrap.min.js',
            // 'js/bootstrap-select.min.js',
            // 'js/jquery.mmenu.all.js',
            'js/ace-responsive-menu.js',
            // 'js/jquery-scrolltofixed-min.js',
            'js/owl.js',
            'js/wow.min.js',
            'plugins/swiper/swiper-bundle.min.js',
            // 'js/parallax.js',
            // 'js/pricing-slider.js',
            'js/script.js',
            'js/x-metr.js',
            // 'js/script-main.js',
            // 'js/filter-map.js',
            // 'plugins/slick.js',
            // 'js/main.js'
        ];

        foreach ($scripts as $script) {
            echo '<script src="' . Theme::asset()->url($script) . '"></script>' . PHP_EOL;
        }
    });

    add_filter('real_estate_dashboard_header', function () {
        return Theme::partial('header');
    });

    Theme::addBodyAttributes(['class' => 'body counter-scroll']);

    RvMedia::addSize('medium-square', 400, 400)
        ->addSize('medium-rectangle-column', 400, 560)
        ->addSize('medium-rectangle', 400, 260);

    add_filter('theme_preloader_versions', function (array $versions): array {
        return [
            ...$versions,
            'v2' => __('Theme built-in'),
        ];
    }, 999);

    add_filter('theme_preloader', function (?string $html): ?string {
        if (theme_option('preloader_version', 'v1') === 'v2') {
            return $html . Theme::partial('preloader');
        }

        return $html;
    }, 999);

    if (is_plugin_active('real-estate')) {
        add_filter('theme_front_footer_content', function (?string $html): ?string {
            if (RealEstateHelper::isLoginEnabled() && theme_option('use_modal_for_authentication', true)) {
                $loginForm = LoginForm::create()
                    ->setFormOption('has_wrapper', 'no');

                $registerForm = null;

                if (RealEstateHelper::isRegisterEnabled()) {
                    $registerForm = RegisterForm::create()
                        ->columns()
                        ->when(! setting('real_estate_hide_username_in_registration_page', false), function (RegisterForm $form): void {
                            $form->modify('phone', PhoneNumberField::class, ['colspan' => 2]);
                        })
                        ->modify('agree_terms_and_policy', OnOffCheckboxField::class, ['colspan' => 2])
                        ->modify('login', HtmlField::class, [
                            'colspan' => 2,
                            'html' => sprintf(
                                '<div class="mt-3">%s <a href="%s" class="text-decoration-underline">%s</a></div>',
                                __('Already have an account?'),
                                route('public.account.login'),
                                __('Login')
                            ),
                        ])
                        ->setFormOption('has_wrapper', 'no');
                }

                $html .= Theme::partial(
                    'modal-authentication',
                    compact('loginForm', 'registerForm')
                );
            }

            if (theme_option('enabled_back_to_top', 'yes') === 'yes') {
                $html .= Theme::partial('go-to-top');
            }

            return $html;
        }, 999);

        add_filter('real_estate_property_status_html', function (?string $html, ?string $value = null): string {
            $color = match ($value) {
                // PropertyStatusEnum::SELLING => 'primary',
                PropertyStatusEnum::RENTING => 'primary',
                default => null,
            };

            return sprintf('<span class="flag-tag %s">%s</span>', $color, PropertyStatusEnum::getLabel($value));
        }, 999, 2);

        // add_filter('real_estate_project_status_html', function (?string $html, string $value): string {
        //     $color = match ($value) {
        //         ProjectStatusEnum::PUBLISHED => 'primary',
        //         default => null,
        //     };

        //     return sprintf('<span class="flag-tag %s">%s</span>', $color, ProjectStatusEnum::getLabel($value));
        // }, 999, 2);

        CategoryForm::extend(function (CategoryForm $form): void {
            $form
                ->addAfter(
                    'is_default',
                    'icon',
                    CoreIconField::class,
                    CoreIconFieldOption::make()
                        ->label(__('Icon'))
                        ->metadata(),
                )
                ->addAfter(
                    'icon',
                    'icon_image',
                    MediaImageField::class,
                    MediaImageFieldOption::make()
                        ->label(__('Icon image'))
                        ->helperText(__('If icon image is set, it will be used instead of the icon above.'))
                        ->metadata()
                );
        });

        PropertyForm::extend(function (PropertyForm $form): void {
            $form
                ->addAfter(
                    'author_id',
                    'video_url',
                    TextField::class,
                    TextFieldOption::make()
                        ->label(__('Video URL'))
                        ->placeholder('https://youtu.be/xxxx')
                        ->helperText(__('Use the YouTube video link to be able to watch the video directly on the website.'))
                        ->metadata()
                )
                ->addAfter(
                    'video_url',
                    'video_thumbnail',
                    MediaImageField::class,
                    MediaImageFieldOption::make()
                        ->label(__('Video thumbnail'))
                        ->helperText(__('If you use the YouTube video link above, the thumbnail will be automatically obtained.'))
                        ->metadata()
                );
        });

        if (RealEstateHelper::isLoginEnabled()) {
            AccountPropertyForm::extend(function (AccountPropertyForm $form): void {
                $form
                    ->addAfter(
                        'unique_id',
                        'video_url',
                        TextField::class,
                        TextFieldOption::make()
                            ->label(__('Video URL'))
                            ->placeholder('https://youtu.be/xxxx')
                            ->helperText(
                                __('Use the YouTube video link to be able to watch the video directly on the website.')
                            )
                            ->metadata()
                    )
                    ->addAfter(
                        'video_url',
                        'video_thumbnail',
                        MediaImageField::class,
                        MediaImageFieldOption::make()
                            ->label(__('Video thumbnail'))
                            ->helperText(
                                __(
                                    'If you use the YouTube video link above, the thumbnail will be automatically obtained.'
                                )
                            )
                            ->metadata()
                    );
            });

            AccountPropertyForm::afterSaving(function (AccountPropertyForm $form): void {
                $request = $form->getRequest();

                $request->validate([
                    'video_url' => ['nullable', 'string', 'url', 'max:255'],
                    'video_thumbnail_input' => ['nullable', new MediaImageRule()],
                ]);

                /**
                 * @var Property $model
                 */
                $model = $form->setRequest($request)->getModel();

                $model->saveMetaDataFromFormRequest('video_thumbnail', $request);
            }, 175);

            AccountForm::extend(function (AccountForm $form) {
                return $form
                    ->addAfter(
                        'closeRow',
                        'social_links',
                        RepeaterField::class,
                        RepeaterFieldOption::make()
                            ->fields(Theme::getSocialLinksRepeaterFields())
                            ->label(__('Social links'))
                            ->metadata()
                            ->toArray()
                    );
            });
        }

        FeatureForm::extend(function (FeatureForm $form): void {
            $form
                ->addAfter(
                    'icon',
                    'featured',
                    OnOffField::class,
                    OnOffFieldOption::make()
                        ->label(__('Featured Amenity'))
                        ->defaultValue(false)
                        ->metadata()
                );
        });
    }

    if (is_plugin_active('contact')) {
        ContactForm::extend(function (ContactForm $form): void {
            $form
                ->setFormInputClass('form-control style-1')
                ->modify(
                    'submit',
                    'submit',
                    [
                        'attr' => ['class' => 'tf-btn primary size-1'],
                        'label' => __('Send Message'),
                    ]
                );
        });
    }

    PageForm::extend(function (PageForm $form): void {
        $form
            ->add(
                'breadcrumb',
                SelectField::class,
                SelectFieldOption::make()
                    ->label(__('Breadcrumb'))
                    ->metadata()
                    ->choices([
                        'yes' => __('Yes'),
                        'no' => __('No'),
                    ]),
            )
            ->add(
                'breadcrumb_background_color',
                ColorField::class,
                ColorFieldOption::make()
                    ->label(__('Breadcrumb background color'))
                    ->defaultValue(theme_option('breadcrumb_background_color', '#f7f7f7'))
                    ->metadata()
            )
            ->add(
                'breadcrumb_text_color',
                ColorField::class,
                ColorFieldOption::make()
                    ->label(__('Breadcrumb text color'))
                    ->defaultValue(theme_option('breadcrumb_text_color', '#161e2d'))
                    ->metadata()
            )
            ->add(
                'breadcrumb_background_image',
                MediaImageField::class,
                MediaImageFieldOption::make()
                    ->label(__('Breadcrumb background image'))
                    ->defaultValue(theme_option('breadcrumb_background_image'))
                    ->metadata()
            );
    });



    add_filter(THEME_FRONT_FOOTER, function (?string $html): string {
        if (AdminHelper::isInAdmin()) {
            return $html;
        }

        if (
            theme_option('facebook_comment_enabled_in_property', 'no') == 'yes'
            || theme_option('facebook_comment_enabled_in_project', 'no') == 'yes'
        ) {
            return $html . view('packages/theme::partials.facebook-integration')->render();
        }

        return $html;
    }, 120);

    add_filter(BASE_FILTER_PUBLIC_COMMENT_AREA, function ($html, ?BaseModel $model = null) {
        if (
            (theme_option('facebook_comment_enabled_in_property', 'yes') == 'yes' && $model instanceof Property) ||
            (theme_option('facebook_comment_enabled_in_project', 'yes') == 'yes' && $model instanceof Project)
        ) {
            return $html . view('packages/theme::partials.facebook-comments')->render();
        }

        return $html;
    }, 120, 2);


    FormAbstract::beforeRendering(function ($form) {
        if ($form instanceof LoginForm) {
            $form->ignoreBaseTemplate();
        }
        if ($form instanceof RegisterForm) {
            $form
                ->remove('last_name')
                ->remove('username')
                ->remove('phone')
                ->remove('password_confirmation')
                ->remove('agree_terms_and_policy');
        }

        if ($form instanceof ProfileForm) {
            $form
                ->modify('first_name', TextField::class,  TextFieldOption::make()->label(__('Your Name')))
                ->remove('last_name')
                ->remove('username')
                ->remove('company')
                ->remove('description')
                ->remove('dob')
                ->remove('gender')
                ->hasFiles()
                ->addBefore(
                    'first_name',
                    'avatar_image',
                    MediaImageField::class,
                    MediaImageFieldOption::make()
                        ->label(trans('plugins/real-estate::dashboard.profile-picture'))
                        ->required()
                        ->helperText('Image in *.jpg or *.png format up to 1 MB in size.')
                        ->attributes(['accept' => 'image/*', 'class' => 'my-avatar'])
                        ->value($form->getModel()->avatar->url)
                )
               ;
        }

        if ($form instanceof AccountForm) {
            $form
                ->setFormOption('autocomplete', 'off')
                ->remove('last_name')
                ->remove('username')
                 ->addAfter(
                    'email',
                    'telegram',
                    TextField::class,
                    TextFieldOption::make()
                        ->label(__('Telegram'))
                        ->metadata()
                )
                ->addAfter(
                    'telegram',
                    'whatsapp',
                    TextField::class,
                    TextFieldOption::make()
                        ->label(__('WhatsApp'))
                        ->attributes(['id' => 'whatsapp_number', 'autocomplete' => 'off', 'inputmode' => 'numeric'])
                        ->metadata()
                );
        }

        if ($form instanceof ProjectForm) {
            $form
                ->modify('location', GoogleMapAutocompleteField::class);
        }

        if ($form instanceof PropertyForm) {
            $form
                ->remove('type')
                ->remove('floor_plans')
                // ->remove('currency_id')
                ->remove('private_notes')
                ->remove('auto_renew')
                ->remove('period')
                ->remove('video_url')
                ->remove('video_thumbnail')
                ->addAfter(
                    'currency_id',
                    'commission',
                    TextField::class,
                    TextFieldOption::make()
                        ->label(__('Commission'))
                        ->wrapperAttributes([
                            'class' => 'form-group mb-3 col-md-6',
                        ])
                        ->metadata()
                )
                // ->add('price', TextField::class, [
                //     'label' => trans('plugins/real-estate::property.form.price'),
                //     'wrapper' => [
                //         'class' => 'form-group mb-3 col-md-6',
                //     ],
                //     'attr' => [
                //         'id' => 'price-number',
                //         'placeholder' => trans('plugins/real-estate::property.form.price'),
                //         'class' => 'form-control input-mask-number',
                //         'data-thousands-separator' => RealEstateHelper::getThousandSeparatorForInputMask(),
                //         'data-decimal-separator' => RealEstateHelper::getDecimalSeparatorForInputMask(),
                //     ],
                // ])
                ->addAfter(
                    'commission',
                    'deposit',
                    TextField::class,
                    TextFieldOption::make()
                        ->label(__('Deposit'))
                        ->wrapperAttributes([
                            'class' => 'form-group mb-3 col-md-6',
                        ])
                        ->attributes([
                            'class' => 'form-control input-mask-number',
                            'data-thousands-separator' => RealEstateHelper::getThousandSeparatorForInputMask(),
                            'data-decimal-separator' => RealEstateHelper::getDecimalSeparatorForInputMask(),
                        ])
                        ->metadata()
                )
                ->modify('location', GoogleMapAutocompleteField::class);
        }

        if ($form instanceof AccountPropertyForm) {
            $form
                ->remove('description')
                // ->modify(
                //     'content',
                //     TextareaField::class,
                //     TextareaFieldOption::make()
                //         ->label(__('Description'))
                //         ->required(),
                //     true
                // )
                // ->remove('latitude')
                // ->remove('longitude')
                ->modify(
                    'number_bedroom',
                    TextField::class
                )
                ->modify(
                    'number_bathroom',
                    TextField::class
                )
                ->modify(
                    'number_floor',
                    TextField::class
                )
                ->modify(
                    'square',
                    TextField::class
                )
                ->modify(
                    'price',
                    TextField::class,
                    TextFieldOption::make()
                    // ->wrapperAttributes([
                    //     'class' => 'form-group mb-3 col-md-6',
                    // ])
                    ->label(__('Price'))
                    ->attributes(['class' => 'form-control'])
                )
                // ->remove('number_floor')
                // ->remove('square')
                // ->remove('currency_id')
                ->remove('private_notes')
                ->remove('never_expired')
                ->remove('auto_renew')
                ->remove('floor_plans')
                // ->remove('location_data')
                ->remove('video_url')
                ->remove('video_thumbnail')
                ->remove('project_id')
                ->remove('categories[]')
                ->remove('unique_id')
                ->remove('status')
                ->removeMetaBox('facilities')
                ->removeMetaBox('custom_fields_box')
                ->modify('location', GoogleMapAutocompleteField::class)
                ->setActionButtons(Theme::partial('job-form-actions'))
            ;
        }

        if ($form instanceof FeatureForm) {
            $form
                ->modify('icon', TextField::class, TextFieldOption::make()->helperText(__('This icon will be used in the frontend for featured amenities.')));
        }
    });

    FormAbstract::afterSaving(function (FormAbstract $form): void {
        if ($form instanceof ProfileForm) {
            $request = $form->getRequest();

            $request->validate([
                'avatar_image_input' => ['nullable', new MediaImageRule()],
            ]);

            /**
             * @var Account $account
             */
            $account = $form->getModel();

            $account->saveMetaDataFromFormRequest('telegram', $request);
            $account->saveMetaDataFromFormRequest('whatsapp', $request);

            $result = RvMedia::handleUpload($request->file('avatar_image_input'), 0, $account->upload_folder);

            if ($result['error']) {
                return;
            }

            $file = $result['data'];

            $avatar = MediaFile::query()->find($account->avatar_id);

            $avatar?->forceDelete();

            $account->avatar_id = $file->id;

            $account->save();
        }
         if ($form instanceof AccountForm) {
             $request = $form->getRequest();
            /**
             * @var Account $account
             */
            $account = $form->getModel();

            $account->saveMetaDataFromFormRequest('telegram', $request);
            $account->saveMetaDataFromFormRequest('whatsapp', $request);
         }

        if ($form instanceof PropertyForm) {
            $request = $form->getRequest();
            $property = $form->getModel();

            $property->saveMetaDataFromFormRequest('commission', $request);
            $property->saveMetaDataFromFormRequest('deposit', $request);


            $form->getModel()->updateQuietly([
                'type' => PropertyTypeEnum::RENT,
                'period' => PropertyPeriodEnum::MONTH,
            ]);
        }

        // if($form instanceof FeatureForm) {
        //     $request = $form->getRequest();

        //     $request->validate([
        //         'featured' => 'nullable|boolean',
        //     ]);

        //     $feature = $form->getModel();

        //     $feature->saveMetaDataFromFormRequest('featured', $request);
        // }

    }, 175);

    add_action(BASE_ACTION_AFTER_CREATE_CONTENT, 'save_addition_metabox_fields', 230, 3);
    add_action(BASE_ACTION_AFTER_UPDATE_CONTENT, 'save_addition_metabox_fields', 231, 3);

    function save_addition_metabox_fields($type, $request, $object) {
        if (get_class($object) ===  Feature::class) {
            MetaBox::saveMetaBoxData($object, 'featured', $request->input('featured'));
        }
    }

    add_filter(BASE_FILTER_BEFORE_RENDER_FORM, function (FormAbstract $form) {
        if ($form instanceof AccountPropertyForm) {
            $form->remove('slug');
        }
        if ($form instanceof ProfileForm) {
            $form->remove('slug');
        }
    }, 1900);



    add_filter('core_request_rules', function (array $rules, Request $request) {
        if ($request instanceof SettingRequest) {
            unset($rules['last_name']);
            unset($rules['username']);
            unset($rules['dob']);
            unset($rules['description']);
            unset($rules['company']);
            unset($rules['gender']);
            $rules['country_id'] = 'required';
        }

        if ($request instanceof RegisterRequest) {
            unset($rules['last_name']);
            unset($rules['username']);
            unset($rules['phone']);
            $rules['password'] = 'required|min:6';
        }
        if($request instanceof AccountPropertyRequest) {
            unset($rules['content']);
        }

        if ($request instanceof AccountCreateRequest || $request instanceof AccountEditRequest) {
            unset($rules['last_name']);
            unset($rules['username']);
        }

        return $rules;
    }, 120, 2);

    add_filter('core_request_messages', function (array $messages, Request $request) {
        if ($request instanceof SettingRequest) {
            $messages['country_id.required'] = 'Please select your country.';
        }

        return $messages;
    }, 120, 2);

    add_filter('cms_table_default_buttons', function (array $buttons) {
        if (! AdminHelper::isInAdmin(true)) {
            $buttons = array_filter($buttons, fn($button) => $button !== 'reload');
        }

        return $buttons;
    }, 120);

    add_filter(
        'real_estate_account_dashboard_header_buttons_before',
        function (?string $html): string {
            return $html . Theme::partial('submit-property-button');
        }
    );

    add_filter(
        'real_estate_account_dashboard_sidebar_top_account_credits_after',
        function (?string $html): string {
            return $html . Theme::partial('submit-property-button-in-sidebar');
        }
    );

    add_filter(
        'real_estate_account_dashboard_sidebar_menu_after',
        function (?string $html): string {
            return $html . Theme::partial('submit-property-button-in-menu');
        }
    );

    DashboardMenu::for('account')->beforeRetrieving(function (DashboardMenuSupport $menu) {
        $menu
            ->removeItem('cms-account-consult')
            ->registerItem([
                'id' => 'cms-account-dashboard-logout',
                'priority' => 900,
                'name' => 'plugins/real-estate::dashboard.logout-cta',
                'url' => fn() => route('public.account.logout'),
                'icon' => 'ti ti-logout',
            ]);
    });

    app('events')->listen(RouteMatched::class, function () {
        if (! auth()->check()) {
            // SeoHelper::removeModule(Property::class);
            // SlugHelper::removeModule(Property::class);
        }
    });

    add_filter('real_estate_account_dashboard_sidebar_top', function () {
        return Theme::getThemeNamespace('views.real-estate.dashboard.layouts.sidebar-top');
    }, 120);
});
