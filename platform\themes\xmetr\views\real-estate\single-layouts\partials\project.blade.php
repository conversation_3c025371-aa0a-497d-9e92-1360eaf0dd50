@if (RealEstateHelper::isEnabledProjects() && $property->project_id && ($project = $property->project))
<a  href="{{ $project->url }}">
<div class="w-full relative rounded-[10px] overflow-hidden x-image-scale h-[300px]">
    <img src="{{ RvMedia::getImageUrl($project->image) }}" alt="{{ $project->name }}" class="w-full h-full object-cover">
    <div class="absolute top-[20px] left-[20px] flex gap-[5px] flex-wrap items-start">  
         @if (RealEstateHelper::isEnabledReview() && $project->reviews_count > 0)
        <div class="px-[8px] py-[5px] bg-white rounded-[5px] w-fit">
       
            <p class="text-[13px] text-black font-bold">⭐ {{  round($project->reviews_avg_star, 1) ?: 0 }} (@if($project->reviews_count === 1)
                {{ __('1 Review') }}
                @else
                    {{ __(':number Reviews', ['number' => $project->reviews_count]) }}
                @endif)
            </p>
        </div>
        @endif
    </div>
    <div class="absolute px-[20px] py-[30px] left-0 bottom-0 flex flex-col gap-[10px] z-[2] w-full">
      <div class="flex flex-col gap-[5px]">
        <p class="text-white text-[15px]">{{ __('Project') }}</p>
        <p class="text-white text-[20px] font-bold">{{ $project->name }}</p>
      </div>
    </div>
    <div class="absolute bottom-0 left-0 w-full h-[95px] rounded-b-[10px]" style="background: linear-gradient(180deg, rgba(33, 35, 41, 0),rgba(33, 35, 41, .8));"></div>
</div>

</a>



    {{-- <div @class(['single-property-project', $class ?? null])>
        <div class="h7 title fw-7">{{ __("Project's Information") }}</div>
        <div class="box-project mt-3">
            <div class="project-thumb">
                <a href="{{ $project->url }}">
                    {{ RvMedia::image($project->image, $project->name) }}
                </a>
            </div>
            <div class="project-info">
                <h5 class="title">
                    <a href="{{ $project->url }}">{{ $project->name }}</a>
                </h5>
                <div class="text-variant-1">
                    {{ Str::limit($project->description, 120) }}
                </div>
                <ul class="meta">
                    @if($project->short_address)
                        <li class="meta-item">
                            <x-core::icon name="ti ti-map-pin" />
                            {{ $project->short_address }}
                        </li>
                    @endif
                    @if ($project->investor->id)
                        <li class="meta-item">
                            <x-core::icon name="ti ti-building" />
                            {{ $project->investor->name }}
                        </li>
                    @elseif ($project->date_finish || $project->date_sell)
                        <li class="meta-item">
                            <x-core::icon name="ti ti-calendar" />
                            {{ Theme::formatDate($project->date_finish ?: $project->date_sell) }}
                        </li>
                    @endif
                </ul>
            </div>
        </div>
    </div> --}}
@endif
