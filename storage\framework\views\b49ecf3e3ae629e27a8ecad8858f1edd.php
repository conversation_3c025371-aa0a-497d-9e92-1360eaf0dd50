<?php if(RealEstateHelper::isEnabledProjects() && $property->project_id && ($project = $property->project)): ?>
<a  href="<?php echo e($project->url); ?>">
<div class="w-full relative rounded-[10px] overflow-hidden x-image-scale h-[300px]">
    <img src="<?php echo e(RvMedia::getImageUrl($project->image)); ?>" alt="<?php echo e($project->name); ?>" class="w-full h-full object-cover">
    <div class="absolute top-[20px] left-[20px] flex gap-[5px] flex-wrap items-start">  
         <?php if(RealEstateHelper::isEnabledReview() && $project->reviews_count > 0): ?>
        <div class="px-[8px] py-[5px] bg-white rounded-[5px] w-fit">
       
            <p class="text-[13px] text-black font-bold">⭐ <?php echo e(round($project->reviews_avg_star, 1) ?: 0); ?> (<?php if($project->reviews_count === 1): ?>
                <?php echo e(__('1 Review')); ?>

                <?php else: ?>
                    <?php echo e(__(':number Reviews', ['number' => $project->reviews_count])); ?>

                <?php endif; ?>)
            </p>
        </div>
        <?php endif; ?>
    </div>
    <div class="absolute px-[20px] py-[30px] left-0 bottom-0 flex flex-col gap-[10px] z-[2] w-full">
      <div class="flex flex-col gap-[5px]">
        <p class="text-white text-[15px]"><?php echo e(__('Project')); ?></p>
        <p class="text-white text-[20px] font-bold"><?php echo e($project->name); ?></p>
      </div>
    </div>
    <div class="absolute bottom-0 left-0 w-full h-[95px] rounded-b-[10px]" style="background: linear-gradient(180deg, rgba(33, 35, 41, 0),rgba(33, 35, 41, .8));"></div>
</div>

</a>



    
<?php endif; ?>
<?php /**PATH D:\laragon\www\xmetr\platform\themes/xmetr/views/real-estate/single-layouts/partials/project.blade.php ENDPATH**/ ?>