{"__meta": {"id": "01K13RX03NF5EH8PYP2FE3RHSJ", "datetime": "2025-07-26 16:26:18", "utime": **********.102515, "method": "DELETE", "uri": "/admin/real-estate/properties/1180", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753547176.156457, "end": **********.102549, "duration": 1.946092128753662, "duration_str": "1.95s", "measures": [{"label": "Booting", "start": 1753547176.156457, "relative_start": 0, "end": **********.768813, "relative_end": **********.768813, "duration": 1.****************, "duration_str": "1.61s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.76886, "relative_start": 1.***************, "end": **********.102553, "relative_end": 3.814697265625e-06, "duration": 0.****************, "duration_str": "334ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.798815, "relative_start": 1.****************, "end": **********.828424, "relative_end": **********.828424, "duration": 0.029608964920043945, "duration_str": "29.61ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.858341, "relative_start": 1.****************, "end": **********.096158, "relative_end": **********.096158, "duration": 0.*****************, "duration_str": "238ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "xmetr.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 18, "nb_statements": 16, "nb_visible_statements": 18, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01899, "accumulated_duration_str": "18.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.844572, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 0, "width_percent": 2.317}, {"sql": "select * from `re_properties` where `id` = '1180' limit 1", "type": "query", "params": [], "bindings": ["1180"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 61}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 959}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.847377, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 2.317, "width_percent": 3.528}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 114}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 900}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 885}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 805}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 144}], "start": **********.940504, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "DeleteResourceAction.php:114", "source": {"index": 9, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 114}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FHttp%2FActions%2FDeleteResourceAction.php:114", "ajax": false, "filename": "DeleteResourceAction.php", "line": "114"}, "connection": "xmetr", "explain": null, "start_percent": 5.845, "width_percent": 0}, {"sql": "delete from `re_property_categories` where `re_property_categories`.`property_id` = 1180", "type": "query", "params": [], "bindings": [1180], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Property.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Property.php", "line": 116}, {"index": 19, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 90}, {"index": 20, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 118}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 900}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 885}], "start": **********.946353, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Property.php:116", "source": {"index": 12, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Property.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Property.php", "line": 116}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FProperty.php:116", "ajax": false, "filename": "Property.php", "line": "116"}, "connection": "xmetr", "explain": null, "start_percent": 5.845, "width_percent": 2.791}, {"sql": "delete from `re_custom_field_values` where `re_custom_field_values`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property' and `re_custom_field_values`.`reference_id` = 1180 and `re_custom_field_values`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property", 1180], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Property.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Property.php", "line": 117}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 90}, {"index": 23, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 118}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 900}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 885}], "start": **********.951236, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "Property.php:117", "source": {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Property.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Property.php", "line": 117}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FProperty.php:117", "ajax": false, "filename": "Property.php", "line": "117"}, "connection": "xmetr", "explain": null, "start_percent": 8.636, "width_percent": 6.793}, {"sql": "delete from `re_reviews` where `re_reviews`.`reviewable_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property' and `re_reviews`.`reviewable_id` = 1180 and `re_reviews`.`reviewable_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property", 1180], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Property.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Property.php", "line": 118}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 90}, {"index": 23, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 118}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 900}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 885}], "start": **********.955148, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Property.php:118", "source": {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Property.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Property.php", "line": 118}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FProperty.php:118", "ajax": false, "filename": "Property.php", "line": "118"}, "connection": "xmetr", "explain": null, "start_percent": 15.429, "width_percent": 3.16}, {"sql": "delete from `re_property_features` where `re_property_features`.`property_id` = 1180", "type": "query", "params": [], "bindings": [1180], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Property.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Property.php", "line": 119}, {"index": 19, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 90}, {"index": 20, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 118}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 900}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 885}], "start": **********.9574358, "duration": 0.0032400000000000003, "duration_str": "3.24ms", "memory": 0, "memory_str": null, "filename": "Property.php:119", "source": {"index": 12, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Property.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Property.php", "line": 119}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FProperty.php:119", "ajax": false, "filename": "Property.php", "line": "119"}, "connection": "xmetr", "explain": null, "start_percent": 18.589, "width_percent": 17.062}, {"sql": "delete from `re_facilities_distances` where `re_facilities_distances`.`reference_id` = 1180 and `reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property'", "type": "query", "params": [], "bindings": [1180, "Xmetr\\RealEstate\\Models\\Property"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Property.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Property.php", "line": 120}, {"index": 19, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 90}, {"index": 20, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 118}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 900}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 885}], "start": **********.963217, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Property.php:120", "source": {"index": 12, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Property.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Property.php", "line": 120}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FProperty.php:120", "ajax": false, "filename": "Property.php", "line": "120"}, "connection": "xmetr", "explain": null, "start_percent": 35.65, "width_percent": 2.159}, {"sql": "delete from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property' and `meta_boxes`.`reference_id` = 1180 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property", 1180], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Property.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Property.php", "line": 121}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 90}, {"index": 23, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 118}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 900}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 885}], "start": **********.965231, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Property.php:121", "source": {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Property.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Property.php", "line": 121}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FProperty.php:121", "ajax": false, "filename": "Property.php", "line": "121"}, "connection": "xmetr", "explain": null, "start_percent": 37.809, "width_percent": 3.739}, {"sql": "delete from `re_properties` where `id` = 1180", "type": "query", "params": [], "bindings": [1180], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 90}, {"index": 15, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 118}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 900}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 885}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 805}], "start": **********.967495, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "DeleteResourceAction.php:90", "source": {"index": 14, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 90}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FHttp%2FActions%2FDeleteResourceAction.php:90", "ajax": false, "filename": "DeleteResourceAction.php", "line": "90"}, "connection": "xmetr", "explain": null, "start_percent": 41.548, "width_percent": 3.844}, {"sql": "select * from `slugs` where `reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property' and `reference_id` = 1180 order by `slugs`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property", 1180], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 145}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 90}, {"index": 25, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 118}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 900}], "start": **********.969547, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 45.392, "width_percent": 4.792}, {"sql": "delete from `meta_boxes` where (`reference_id` = 1180 and `reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property')", "type": "query", "params": [], "bindings": [1180, "Xmetr\\RealEstate\\Models\\Property"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/base/src/Listeners/DeletedContentListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Listeners\\DeletedContentListener.php", "line": 25}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 92}, {"index": 19, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 118}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 900}], "start": **********.974324, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "DeletedContentListener.php:25", "source": {"index": 12, "namespace": null, "name": "platform/core/base/src/Listeners/DeletedContentListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Listeners\\DeletedContentListener.php", "line": 25}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FListeners%2FDeletedContentListener.php:25", "ajax": false, "filename": "DeletedContentListener.php", "line": "25"}, "connection": "xmetr", "explain": null, "start_percent": 50.184, "width_percent": 3.423}, {"sql": "delete from `meta_boxes` where (`meta_key` = 'seo_meta' and `reference_id` = 1180 and `reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property')", "type": "query", "params": [], "bindings": ["seo_meta", 1180, "Xmetr\\RealEstate\\Models\\Property"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 201}, {"index": 14, "namespace": null, "name": "platform/packages/seo-helper/src/SeoHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\seo-helper\\src\\SeoHelper.php", "line": 189}, {"index": 16, "namespace": null, "name": "platform/packages/seo-helper/src/Listeners/DeletedContentListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\seo-helper\\src\\Listeners\\DeletedContentListener.php", "line": 15}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 92}], "start": **********.9790962, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "MetaBox.php:201", "source": {"index": 12, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 201}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FSupports%2FMetaBox.php:201", "ajax": false, "filename": "MetaBox.php", "line": "201"}, "connection": "xmetr", "explain": null, "start_percent": 53.607, "width_percent": 3.265}, {"sql": "delete from `slugs` where (`reference_id` = 1180 and `reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property')", "type": "query", "params": [], "bindings": [1180, "Xmetr\\RealEstate\\Models\\Property"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/packages/slug/src/Listeners/DeletedContentListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\slug\\src\\Listeners\\DeletedContentListener.php", "line": 18}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 92}, {"index": 19, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 118}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 900}], "start": **********.989691, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "DeletedContentListener.php:18", "source": {"index": 12, "namespace": null, "name": "platform/packages/slug/src/Listeners/DeletedContentListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\slug\\src\\Listeners\\DeletedContentListener.php", "line": 18}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FListeners%2FDeletedContentListener.php:18", "ajax": false, "filename": "DeletedContentListener.php", "line": "18"}, "connection": "xmetr", "explain": null, "start_percent": 56.872, "width_percent": 3.791}, {"sql": "select `lang_id` from `languages` where `lang_is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 938}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 963}, {"index": 20, "namespace": null, "name": "platform/plugins/language/src/Listeners/DeletedContentListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Listeners\\DeletedContentListener.php", "line": 15}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}], "start": **********.992807, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 60.664, "width_percent": 8.478}, {"sql": "insert into `audit_histories` (`user_agent`, `ip_address`, `module`, `action`, `user_id`, `reference_user`, `reference_id`, `reference_name`, `type`, `created_at`, `updated_at`, `request`) values ('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '127.0.0.1', 'property', 'deleted', 1, 1, 1180, 'ID: 1180', 'danger', '2025-07-26 16:26:17', '2025-07-26 16:26:17', '{\\\"_method\\\":\\\"delete\\\"}')", "type": "query", "params": [], "bindings": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "127.0.0.1", "property", "deleted", 1, 1, 1180, "ID: 1180", "danger", "2025-07-26 16:26:17", "2025-07-26 16:26:17", "{\"_method\":\"delete\"}"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 60}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 92}, {"index": 23, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 118}], "start": **********.0166419, "duration": 0.00495, "duration_str": "4.95ms", "memory": 0, "memory_str": null, "filename": "AuditHandlerListener.php:60", "source": {"index": 11, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 60}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fsrc%2FListeners%2FAuditHandlerListener.php:60", "ajax": false, "filename": "AuditHandlerListener.php", "line": "60"}, "connection": "xmetr", "explain": null, "start_percent": 69.142, "width_percent": 26.066}, {"sql": "delete from `meta_boxes` where (`meta_key` = 'faq_schema_config' and `reference_id` = 1180 and `reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property')", "type": "query", "params": [], "bindings": ["faq_schema_config", 1180, "Xmetr\\RealEstate\\Models\\Property"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 201}, {"index": 14, "namespace": null, "name": "platform/plugins/faq/src/Listeners/DeletedContentListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\faq\\src\\Listeners\\DeletedContentListener.php", "line": 15}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 20, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 92}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 118}], "start": **********.0247478, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "MetaBox.php:201", "source": {"index": 12, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 201}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FSupports%2FMetaBox.php:201", "ajax": false, "filename": "MetaBox.php", "line": "201"}, "connection": "xmetr", "explain": null, "start_percent": 95.208, "width_percent": 4.792}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 122}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 900}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 885}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 805}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 144}], "start": **********.094527, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "DeleteResourceAction.php:122", "source": {"index": 9, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 122}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FHttp%2FActions%2FDeleteResourceAction.php:122", "ajax": false, "filename": "DeleteResourceAction.php", "line": "122"}, "connection": "xmetr", "explain": null, "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"Xmetr\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Property": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FProperty.php:1", "ajax": false, "filename": "Property.php", "line": "?"}}, "Xmetr\\Language\\Models\\Language": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php:1", "ajax": false, "filename": "Language.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://xmetr.gc/admin/real-estate/properties/1180", "action_name": "property.destroy", "controller_action": "Xmetr\\RealEstate\\Http\\Controllers\\PropertyController@destroy", "uri": "DELETE admin/real-estate/properties/{property}", "controller": "Xmetr\\RealEstate\\Http\\Controllers\\PropertyController@destroy<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FPropertyController.php:174\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Xmetr\\RealEstate\\Http\\Controllers", "prefix": "admin/real-estate/properties", "file": "<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FPropertyController.php:174\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/real-estate/src/Http/Controllers/PropertyController.php:174-177</a>", "middleware": "web, core, auth", "duration": "1.95s", "peak_memory": "44MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-598136136 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-598136136\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1137129276 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1137129276\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-598872882 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">20</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkxQU3o3VFNHaFdyaWJjZVVrNUlUQmc9PSIsInZhbHVlIjoidXB4eWFUdEFpeVc3ZjJ6OWZHVEhxUXFhMG5jN1dFVmtURzBFaHAyNks5Y1YwbWpITzRsZk9FYmNkOUdPOFoxcGhZZ3o3eU1NRVBqKzVNcmhKSFhZL2p2K1BSbVkxTUNVaVhueFFBZ2NTcU9JUC9VZU85eEpTbDY3R1RyOHhwZmoiLCJtYWMiOiI3ODA3MDBiNjk1MzkwMTQyY2VlYzFjNDA3ZDIxMWUxYWUwZTc3M2NlZWU1ZTIxNzFkYTQ2M2VkNjg2YjFiYTgzIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">https://xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">https://xmetr.gc/admin/real-estate/properties</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1709 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjE5eVFoNXI3S3dCZTVpWVUvb1ZlTkE9PSIsInZhbHVlIjoiVFFEOHg1MHRNUCtIQndDL1FUdThDSVJxSzFQTTlRRWNjUGJtalZRTmpvRFQrQ1UybGVrWnR6dUN0ZU16VVJXRWg0UVg5cFdaQmlmL212RUdPSjJLcUgray8wa3JyeFU3LzUrRUs3by9jVXM1cCt0MDZCTXZjczI2RTRNR0xqeTBJT1dpcWNNQTdEeW8wYkZ1S3lUc1pZSVFCd2FiN2EvdTRmZGdiU2FOYVREVUtPdGw5SG1xOUZHVU5Db0Zwa1lvVTRXRDFPOXBhNnZOQ3hkWjJjU2JtaDRQRU9WYXRwczhPTmM1Wit2aThBcz0iLCJtYWMiOiI0NThmZWI3NDI2NThmMTIxOWRlN2JiMjljY2Y3MjQzMDUzNmQyYjA5NzllZTg4YmY2ZTc5MGExYjEzODJhMGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.2064844405.1743885089; cookie_for_consent=1; _hjSessionUser_6417422=eyJpZCI6IjhkMzA2ODc1LTcwNjctNTU3Yi1hNDljLTdhNmEyNTdkM2QxOCIsImNyZWF0ZWQiOjE3NDgzODAwMDM5NTcsImV4aXN0aW5nIjp0cnVlfQ==; wishlist=657; project_wishlist=25; _hjSession_6417422=eyJpZCI6ImQzMGMxNDdkLWIzYjAtNGUzMi1hMDU3LWNjYWIzYWVmODhjNyIsImMiOjE3NTM1NDU3MDQ3MzQsInMiOjEsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjowfQ==; _ga_KQ6X76DET4=GS2.1.s1753545705$o107$g1$t1753547136$j60$l0$h0; XSRF-TOKEN=eyJpdiI6IkxQU3o3VFNHaFdyaWJjZVVrNUlUQmc9PSIsInZhbHVlIjoidXB4eWFUdEFpeVc3ZjJ6OWZHVEhxUXFhMG5jN1dFVmtURzBFaHAyNks5Y1YwbWpITzRsZk9FYmNkOUdPOFoxcGhZZ3o3eU1NRVBqKzVNcmhKSFhZL2p2K1BSbVkxTUNVaVhueFFBZ2NTcU9JUC9VZU85eEpTbDY3R1RyOHhwZmoiLCJtYWMiOiI3ODA3MDBiNjk1MzkwMTQyY2VlYzFjNDA3ZDIxMWUxYWUwZTc3M2NlZWU1ZTIxNzFkYTQ2M2VkNjg2YjFiYTgzIiwidGFnIjoiIn0%3D; xmetr_session=eyJpdiI6IjIrenJpTWJLR1pKUE5LdElKK01nTWc9PSIsInZhbHVlIjoiWHdremhFOSt4UlNMUDhrRUxiODVReFNSZ1o2d3FEMjZ0Z1gwRmFYMjBjZXh0NUtMNmhvZVBQVE9OU1NuTHJNdTFGeXpVcFErNkJCOVNsKzNBSXpUbU41QXhPalhTZENIeE5KaXdUSk5MajNacWN0d0czMHIxM29EbTlSZXlMeWwiLCJtYWMiOiIyMGRiNTZmZWQ1Y2U1YjJjZjI0MDAwMDk4Y2VmMzQxMmUyZTljMmU1MzE0ZTQ5MTFhZjZjNjAyMTNiMWEyNDJmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-598872882\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1226879124 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|1uQwj4bI0uiYsJi0APplQif6Zji5Zq40vBOmafc1oj6NYK3vu89uqM3GLC7O|$2y$04$Astns3SXblBceX03zRicBuVKoyIRRFaOY1GuvREgnRYhHEelfNbdu</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>_hjSessionUser_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>project_wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_hjSession_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_KQ6X76DET4</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ytAjAn3gHkXOl5zsmwHgtRY8qTqW4NuHlaKuh6OF</span>\"\n  \"<span class=sf-dump-key>xmetr_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2vBKbIuUvKuaQ8TwrzXlARgwysIuoj6o6VDmQW3H</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1226879124\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1513115650 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 26 Jul 2025 16:26:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1513115650\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-987749413 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ytAjAn3gHkXOl5zsmwHgtRY8qTqW4NuHlaKuh6OF</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">https://xmetr.gc/admin/real-estate/properties</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>viewed_property</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1172</span> => <span class=sf-dump-num>1753547020</span>\n  </samp>]\n  \"<span class=sf-dump-key>viewed_property_daily</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1172</span> => <span class=sf-dump-num>1753547020</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-987749413\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://xmetr.gc/admin/real-estate/properties/1180", "action_name": "property.destroy", "controller_action": "Xmetr\\RealEstate\\Http\\Controllers\\PropertyController@destroy"}, "badge": null}}