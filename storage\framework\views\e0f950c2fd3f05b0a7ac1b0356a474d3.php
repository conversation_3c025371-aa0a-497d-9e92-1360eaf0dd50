<div class="collapse rental-period-content" id="rental-period-content">
    <div class="rental-period-options">
        <?php if($rentalPeriodOptions && count($rentalPeriodOptions) > 0): ?>
            <?php $__currentLoopData = $rentalPeriodOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <label class="form-check form-check-inline">
                <input class="form-check-input" label="<?php echo e($label); ?>" type="radio" name="rental_period" value="<?php echo e($value); ?>" <?php echo e($selectedRentalPeriod == $value ? 'checked' : ''); ?>>
                <span class="form-check-label"><?php echo e($label); ?></span>
            </label>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php else: ?>
            <p class="text-muted"><?php echo e(__('No rental period options available')); ?></p>
        <?php endif; ?>
    </div>
</div>

<style>
.rental-period-options {
    padding: 1rem 0;
}

.rental-period-options .form-check {
    margin-bottom: 0.75rem;
}

.rental-period-options .form-check:last-child {
    margin-bottom: 0;
}

.metabox-toggle {
    border: none;
    background: transparent;
    padding: 0.25rem 0.5rem;
    color: #6c757d;
    transition: all 0.2s ease;
}

.metabox-toggle:hover {
    color: #495057;
    background-color: #f8f9fa;
}

.metabox-toggle i {
    transition: transform 0.2s ease;
}

.metabox-toggle[aria-expanded="true"] i {
    transform: rotate(180deg);
}

.collapsible-metabox .card-header {
    cursor: pointer;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize metabox collapse functionality
    const toggleButtons = document.querySelectorAll('.metabox-toggle');

    toggleButtons.forEach(function(button) {
        const target = button.getAttribute('data-bs-target');
        const targetElement = document.querySelector(target);

        if (targetElement) {
            // Set initial state (collapsed)
            targetElement.classList.remove('show');
            button.setAttribute('aria-expanded', 'false');

            button.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const isExpanded = button.getAttribute('aria-expanded') === 'true';

                if (isExpanded) {
                    // Collapse
                    targetElement.classList.remove('show');
                    button.setAttribute('aria-expanded', 'false');
                } else {
                    // Expand
                    targetElement.classList.add('show');
                    button.setAttribute('aria-expanded', 'true');
                }
            });
        }
    });
});
</script>


<?php /**PATH D:\laragon\www\xmetr\platform/plugins/real-estate/resources/views/account/forms/rental-period-metabox.blade.php ENDPATH**/ ?>