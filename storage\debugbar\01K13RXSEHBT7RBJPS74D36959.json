{"__meta": {"id": "01K13RXSEHBT7RBJPS74D36959", "datetime": "2025-07-26 16:26:44", "utime": ********04.051585, "method": "POST", "uri": "/admin/real-estate/properties/edit/1171", "ip": "127.0.0.1"}, "messages": {"count": 1, "messages": [{"message": "[16:26:43] LOG.error: Value \"\" is not part of the enum Xmetr\\RealEstate\\Enums\\RentalPeriodEnum", "message_html": null, "is_string": false, "label": "error", "time": **********.553443, "xdebug_link": null, "collector": "log"}]}, "time": {"start": ********01.92418, "end": ********04.051622, "duration": 2.127441883087158, "duration_str": "2.13s", "measures": [{"label": "Booting", "start": ********01.92418, "relative_start": 0, "end": **********.03919, "relative_end": **********.03919, "duration": 1.****************, "duration_str": "1.12s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.03921, "relative_start": 1.**************, "end": ********04.051625, "relative_end": 3.0994415283203125e-06, "duration": 1.****************, "duration_str": "1.01s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.068309, "relative_start": 1.****************, "end": **********.096586, "relative_end": **********.096586, "duration": 0.028276920318603516, "duration_str": "28.28ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: plugins/real-estate::partials.form-features", "start": **********.560166, "relative_start": 1.****************, "end": **********.560166, "relative_end": **********.560166, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.567539, "relative_start": 1.****************, "end": **********.567539, "relative_end": **********.567539, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.570133, "relative_start": 1.6459529399871826, "end": **********.570133, "relative_end": **********.570133, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.572619, "relative_start": 1.6484389305114746, "end": **********.572619, "relative_end": **********.572619, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.575572, "relative_start": 1.6513919830322266, "end": **********.575572, "relative_end": **********.575572, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.582074, "relative_start": 1.6578938961029053, "end": **********.582074, "relative_end": **********.582074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.588255, "relative_start": 1.6640748977661133, "end": **********.588255, "relative_end": **********.588255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.593286, "relative_start": 1.6691060066223145, "end": **********.593286, "relative_end": **********.593286, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.598041, "relative_start": 1.673861026763916, "end": **********.598041, "relative_end": **********.598041, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.602348, "relative_start": 1.6781680583953857, "end": **********.602348, "relative_end": **********.602348, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.605274, "relative_start": 1.6810939311981201, "end": **********.605274, "relative_end": **********.605274, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.607957, "relative_start": 1.68377685546875, "end": **********.607957, "relative_end": **********.607957, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.613323, "relative_start": 1.6891429424285889, "end": **********.613323, "relative_end": **********.613323, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.618498, "relative_start": 1.6943180561065674, "end": **********.618498, "relative_end": **********.618498, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.620967, "relative_start": 1.696786880493164, "end": **********.620967, "relative_end": **********.620967, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.623245, "relative_start": 1.6990649700164795, "end": **********.623245, "relative_end": **********.623245, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.625741, "relative_start": 1.7015609741210938, "end": **********.625741, "relative_end": **********.625741, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.629804, "relative_start": 1.7056238651275635, "end": **********.629804, "relative_end": **********.629804, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.632386, "relative_start": 1.7082059383392334, "end": **********.632386, "relative_end": **********.632386, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.635655, "relative_start": 1.711474895477295, "end": **********.635655, "relative_end": **********.635655, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.637873, "relative_start": 1.7136929035186768, "end": **********.637873, "relative_end": **********.637873, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.639837, "relative_start": 1.7156569957733154, "end": **********.639837, "relative_end": **********.639837, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/real-estate::partials.form-suitable", "start": **********.640651, "relative_start": 1.7164709568023682, "end": **********.640651, "relative_end": **********.640651, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.641119, "relative_start": 1.7169389724731445, "end": **********.641119, "relative_end": **********.641119, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.642088, "relative_start": 1.7179079055786133, "end": **********.642088, "relative_end": **********.642088, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.643831, "relative_start": 1.7196509838104248, "end": **********.643831, "relative_end": **********.643831, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/real-estate::partials.moderation-status", "start": **********.653384, "relative_start": 1.7292039394378662, "end": **********.653384, "relative_end": **********.653384, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.badge", "start": **********.656587, "relative_start": 1.7324068546295166, "end": **********.656587, "relative_end": **********.656587, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.94815, "relative_start": 2.023969888687134, "end": ********04.048314, "relative_end": ********04.048314, "duration": 0.10016417503356934, "duration_str": "100ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 47288136, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "xmetr.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 28, "nb_templates": 28, "templates": [{"name": "plugins/real-estate::partials.form-features", "param_count": null, "params": [], "start": **********.560103, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/real-estate/resources/views/partials/form-features.blade.phpplugins/real-estate::partials.form-features", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fresources%2Fviews%2Fpartials%2Fform-features.blade.php:1", "ajax": false, "filename": "form-features.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.567509, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.5701, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.572594, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.575522, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.581984, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.588201, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.593247, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.597997, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.602305, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.605241, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.607924, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.613274, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.618472, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.620938, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.623219, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.625697, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.629775, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.632358, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.635629, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.63785, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.639815, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "plugins/real-estate::partials.form-suitable", "param_count": null, "params": [], "start": **********.640629, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/real-estate/resources/views/partials/form-suitable.blade.phpplugins/real-estate::partials.form-suitable", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fresources%2Fviews%2Fpartials%2Fform-suitable.blade.php:1", "ajax": false, "filename": "form-suitable.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.641085, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.642047, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.643788, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "plugins/real-estate::partials.moderation-status", "param_count": null, "params": [], "start": **********.653257, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/real-estate/resources/views/partials/moderation-status.blade.phpplugins/real-estate::partials.moderation-status", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fresources%2Fviews%2Fpartials%2Fmoderation-status.blade.php:1", "ajax": false, "filename": "moderation-status.blade.php", "line": "?"}}, {"name": "core/base::components.badge", "param_count": null, "params": [], "start": **********.656562, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/badge.blade.phpcore/base::components.badge", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fbadge.blade.php:1", "ajax": false, "filename": "badge.blade.php", "line": "?"}}]}, "queries": {"count": 29, "nb_statements": 29, "nb_visible_statements": 29, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03807, "accumulated_duration_str": "38.07ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.120007, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 0, "width_percent": 1.418}, {"sql": "select * from `re_properties` where `re_properties`.`id` = '1171' limit 1", "type": "query", "params": [], "bindings": ["1171"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 19, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/PropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\PropertyController.php", "line": 127}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.339926, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 1.418, "width_percent": 2.758}, {"sql": "select `name`, `id` from `re_projects` order by `created_at` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 71}, {"index": 17, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 18, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}, {"index": 19, "namespace": null, "name": "platform/core/base/src/Forms/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormBuilder.php", "line": 11}], "start": **********.346017, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 4.177, "width_percent": 1.418}, {"sql": "select `title`, `id` from `re_currencies`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 75}, {"index": 15, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 16, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}, {"index": 17, "namespace": null, "name": "platform/core/base/src/Forms/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormBuilder.php", "line": 11}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 570}], "start": **********.3608708, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "PropertyForm.php:75", "source": {"index": 14, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 75}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FForms%2FPropertyForm.php:75", "ajax": false, "filename": "PropertyForm.php", "line": "75"}, "connection": "xmetr", "explain": null, "start_percent": 5.595, "width_percent": 2.889}, {"sql": "select `category_id` from `re_categories` inner join `re_property_categories` on `re_categories`.`id` = `re_property_categories`.`category_id` where `re_property_categories`.`property_id` = 1171", "type": "query", "params": [], "bindings": [1171], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 84}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 19, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}, {"index": 20, "namespace": null, "name": "platform/core/base/src/Forms/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormBuilder.php", "line": 11}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 570}], "start": **********.4117968, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "PropertyForm.php:84", "source": {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 84}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FForms%2FPropertyForm.php:84", "ajax": false, "filename": "PropertyForm.php", "line": "84"}, "connection": "xmetr", "explain": null, "start_percent": 8.484, "width_percent": 1.576}, {"sql": "select `id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = 1171", "type": "query", "params": [], "bindings": [1171], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 94}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 19, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}, {"index": 20, "namespace": null, "name": "platform/core/base/src/Forms/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormBuilder.php", "line": 11}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 570}], "start": **********.414896, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "PropertyForm.php:94", "source": {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 94}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FForms%2FPropertyForm.php:94", "ajax": false, "filename": "PropertyForm.php", "line": "94"}, "connection": "xmetr", "explain": null, "start_percent": 10.06, "width_percent": 1.366}, {"sql": "select `id`, `name` from `re_features`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 99}, {"index": 17, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 18, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}, {"index": 19, "namespace": null, "name": "platform/core/base/src/Forms/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormBuilder.php", "line": 11}], "start": **********.416789, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 11.426, "width_percent": 1.313}, {"sql": "select `id`, `name` from `re_facilities`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 123}, {"index": 17, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 18, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}, {"index": 19, "namespace": null, "name": "platform/core/base/src/Forms/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormBuilder.php", "line": 11}], "start": **********.4564009, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 12.74, "width_percent": 0.998}, {"sql": "select `re_facilities`.`id`, `distance`, `re_facilities_distances`.`reference_id` as `pivot_reference_id`, `re_facilities_distances`.`facility_id` as `pivot_facility_id`, `re_facilities_distances`.`reference_type` as `pivot_reference_type`, `re_facilities_distances`.`distance` as `pivot_distance` from `re_facilities` inner join `re_facilities_distances` on `re_facilities`.`id` = `re_facilities_distances`.`facility_id` where `re_facilities_distances`.`reference_id` = 1171 and `re_facilities_distances`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property'", "type": "query", "params": [], "bindings": [1171, "Xmetr\\RealEstate\\Models\\Property"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 134}, {"index": 16, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 17, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormBuilder.php", "line": 11}, {"index": 19, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 570}], "start": **********.500431, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "PropertyForm.php:134", "source": {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 134}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FForms%2FPropertyForm.php:134", "ajax": false, "filename": "PropertyForm.php", "line": "134"}, "connection": "xmetr", "explain": null, "start_percent": 13.738, "width_percent": 1.707}, {"sql": "select `id`, `name`, `parent_id` from `re_categories` where `status` = 'published'", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 126}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/helpers/helpers.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\helpers\\helpers.php", "line": 38}, {"index": 19, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 20, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}], "start": **********.6641598, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 15.445, "width_percent": 2.522}, {"sql": "select * from `re_categories_translations` where `re_categories_translations`.`re_categories_id` in (4, 7, 8) and `re_categories_translations`.`lang_code` = 'en_US'", "type": "query", "params": [], "bindings": ["en_US"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 126}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/helpers/helpers.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\helpers\\helpers.php", "line": 38}, {"index": 25, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}], "start": **********.671234, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 17.967, "width_percent": 1.865}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 626}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Conditionable/Traits/Conditionable.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Conditionable\\Traits\\Conditionable.php", "line": 34}, {"index": 25, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 625}], "start": **********.688529, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 19.832, "width_percent": 1.524}, {"sql": "select * from `cities` where `cities`.`id` = 8189 limit 1", "type": "query", "params": [], "bindings": [8189], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 626}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Conditionable/Traits/Conditionable.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Conditionable\\Traits\\Conditionable.php", "line": 34}, {"index": 25, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 625}, {"index": 26, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}], "start": **********.6916301, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 21.355, "width_percent": 1.655}, {"sql": "update `re_properties` set `type` = 'rent', `project_id` = '24', `period` = 'month', `rental_period` = '', `status` = 'renting', `moderation_status` = 'approved', `re_properties`.`updated_at` = '2025-07-26 16:26:43' where `id` = 1171", "type": "query", "params": [], "bindings": [{"value": "rent", "label": "Rent"}, "24", {"value": "month", "label": "Monthly"}, {"value": null, "label": ""}, {"value": "renting", "label": "Renting"}, {"value": "approved", "label": "Approved"}, "2025-07-26 16:26:43", 1171], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/PropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\PropertyController.php", "line": 149}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/PropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\PropertyController.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.769723, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "PropertyController.php:149", "source": {"index": 14, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/PropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\PropertyController.php", "line": 149}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FPropertyController.php:149", "ajax": false, "filename": "PropertyController.php", "line": "149"}, "connection": "xmetr", "explain": null, "start_percent": 23.01, "width_percent": 10.192}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'seo_meta' and `reference_id` = 1171 and `reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property') limit 1", "type": "query", "params": [], "bindings": ["seo_meta", 1171, "Xmetr\\RealEstate\\Models\\Property"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 147}, {"index": 20, "namespace": null, "name": "platform/packages/seo-helper/src/SeoHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\seo-helper\\src\\SeoHelper.php", "line": 171}, {"index": 22, "namespace": null, "name": "platform/packages/seo-helper/src/Listeners/UpdatedContentListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\seo-helper\\src\\Listeners\\UpdatedContentListener.php", "line": 15}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}], "start": **********.8191192, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 33.202, "width_percent": 1.655}, {"sql": "select * from `slugs` where (`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property' and `reference_id` = 1171) limit 1", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property", 1171], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/packages/slug/src/Listeners/UpdatedContentListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\slug\\src\\Listeners\\UpdatedContentListener.php", "line": 49}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 23, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 514}, {"index": 24, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/PropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\PropertyController.php", "line": 151}], "start": **********.830409, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 34.857, "width_percent": 2.154}, {"sql": "select `lang_id` from `languages` where `lang_is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 938}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 889}, {"index": 20, "namespace": null, "name": "platform/plugins/language/src/Listeners/UpdatedContentListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Listeners\\UpdatedContentListener.php", "line": 16}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}], "start": **********.835218, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 37.011, "width_percent": 1.944}, {"sql": "insert into `audit_histories` (`user_agent`, `ip_address`, `module`, `action`, `user_id`, `reference_user`, `reference_id`, `reference_name`, `type`, `created_at`, `updated_at`, `request`) values ('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '127.0.0.1', 'property', 'updated', 1, 1, 1171, 'Long-term rent of a 3-room apartment 70m² in Paris, France', 'primary', '2025-07-26 16:26:43', '2025-07-26 16:26:43', '{\\\"name\\\":\\\"Long-term rent of a 3-room apartment 70m\\\\u00b2 in Paris, France\\\",\\\"model\\\":\\\"Xmetr\\\\\\\\RealEstate\\\\\\\\Models\\\\\\\\Property\\\",\\\"slug\\\":\\\"long-term-rent-of-a-3-room-apartment-70m2-in-paris-france-2\\\",\\\"slug_id\\\":\\\"2886\\\",\\\"is_slug_editable\\\":\\\"1\\\",\\\"description\\\":null,\\\"original_description\\\":\\\"3\\\\u0445\\\\u043a\\\\u043e\\\\u043c\\\\u043d\\\\u0430\\\\u0442\\\\u043d\\\\u0430\\\\u044f \\\\u043a\\\\u0432\\\\u0430\\\\u0440\\\\u0442\\\\u0438\\\\u0440\\\\u0430\\\\r\\\\n70\\\\u043c2\\\\r\\\\n3\\\\u0439 \\\\u044d\\\\u0442\\\\u0430\\\\u0436 \\\\r\\\\nRue Salneuve 75017, \\\\u041f\\\\u0430\\\\u0440\\\\u0438\\\\u0436 \\\\r\\\\n3490\\\\u20ac \\\\/ \\\\u043c\\\\u0435\\\\u0441\\\\u044f\\\\u0446 \\\\r\\\\n\\\\u0421\\\\u0432\\\\u043e\\\\u0431\\\\u043e\\\\u0434\\\\u043d\\\\u0430 \\\\u0441 30\\\\/06\\\",\\\"is_featured\\\":\\\"0\\\",\\\"content\\\":\\\"<p>3 room apartment<br>70m2<br>3rd floor<br>Rue Salneuve 75017, Paris<br>3490\\\\u20ac \\\\/ month<br>Available from 30\\\\/06<\\\\/p>\\\",\\\"images\\\":[null,\\\".\\\\/photo-2025-06-19-205916.webp\\\",\\\".\\\\/photo-2025-06-19-205919.webp\\\",\\\".\\\\/photo-2025-06-19-205918.webp\\\",\\\".\\\\/photo-2025-06-19-205917.webp\\\",\\\".\\\\/photo-2025-06-19-205915.webp\\\",\\\".\\\\/photo-2025-06-19-205920.webp\\\"],\\\"video\\\":null,\\\"country_id\\\":\\\"27\\\",\\\"state_id\\\":null,\\\"city_id\\\":\\\"8189\\\",\\\"district_id\\\":null,\\\"location\\\":\\\"Rue Salneuve, 75017 \\\\u041f\\\\u0430\\\\u0440\\\\u0438\\\\u0436, \\\\u0424\\\\u0440\\\\u0430\\\\u043d\\\\u0446\\\\u0456\\\\u044f\\\",\\\"latitude\\\":\\\"48.8853784\\\",\\\"longitude\\\":\\\"2.3143739\\\",\\\"number_bedroom\\\":null,\\\"number_bathroom\\\":null,\\\"number_floor\\\":\\\"3\\\",\\\"square\\\":\\\"70\\\",\\\"price\\\":\\\"3490\\\",\\\"currency_id\\\":\\\"4\\\",\\\"commission\\\":null,\\\"deposit\\\":null,\\\"never_expired\\\":\\\"1\\\",\\\"bills_included\\\":\\\"0\\\",\\\"utilities\\\":null,\\\"furnished\\\":\\\"1\\\",\\\"pets_allowed\\\":\\\"0\\\",\\\"smoking_allowed\\\":\\\"0\\\",\\\"online_view_tour\\\":\\\"0\\\",\\\"features\\\":[\\\"14\\\"],\\\"seo_meta\\\":{\\\"seo_title\\\":null,\\\"seo_description\\\":null,\\\"index\\\":\\\"index\\\"},\\\"seo_meta_image\\\":null,\\\"submitter\\\":\\\"apply\\\",\\\"language\\\":\\\"en_US\\\",\\\"status\\\":\\\"renting\\\",\\\"categories\\\":[\\\"7\\\"],\\\"unique_id\\\":null,\\\"project_id\\\":\\\"24\\\",\\\"author_id\\\":\\\"29\\\"}')", "type": "query", "params": [], "bindings": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "127.0.0.1", "property", "updated", 1, 1, 1171, "Long-term rent of a 3-room apartment 70m² in Paris, France", "primary", "2025-07-26 16:26:43", "2025-07-26 16:26:43", "{\"name\":\"Long-term rent of a 3-room apartment 70m\\u00b2 in Paris, France\",\"model\":\"Xmetr\\\\RealEstate\\\\Models\\\\Property\",\"slug\":\"long-term-rent-of-a-3-room-apartment-70m2-in-paris-france-2\",\"slug_id\":\"2886\",\"is_slug_editable\":\"1\",\"description\":null,\"original_description\":\"3\\u0445\\u043a\\u043e\\u043c\\u043d\\u0430\\u0442\\u043d\\u0430\\u044f \\u043a\\u0432\\u0430\\u0440\\u0442\\u0438\\u0440\\u0430\\r\\n70\\u043c2\\r\\n3\\u0439 \\u044d\\u0442\\u0430\\u0436 \\r\\nRue Salneuve 75017, \\u041f\\u0430\\u0440\\u0438\\u0436 \\r\\n3490\\u20ac \\/ \\u043c\\u0435\\u0441\\u044f\\u0446 \\r\\n\\u0421\\u0432\\u043e\\u0431\\u043e\\u0434\\u043d\\u0430 \\u0441 30\\/06\",\"is_featured\":\"0\",\"content\":\"<p>3 room apartment<br>70m2<br>3rd floor<br>Rue Salneuve 75017, Paris<br>3490\\u20ac \\/ month<br>Available from 30\\/06<\\/p>\",\"images\":[null,\".\\/photo-2025-06-19-205916.webp\",\".\\/photo-2025-06-19-205919.webp\",\".\\/photo-2025-06-19-205918.webp\",\".\\/photo-2025-06-19-205917.webp\",\".\\/photo-2025-06-19-205915.webp\",\".\\/photo-2025-06-19-205920.webp\"],\"video\":null,\"country_id\":\"27\",\"state_id\":null,\"city_id\":\"8189\",\"district_id\":null,\"location\":\"Rue Salneuve, 75017 \\u041f\\u0430\\u0440\\u0438\\u0436, \\u0424\\u0440\\u0430\\u043d\\u0446\\u0456\\u044f\",\"latitude\":\"48.8853784\",\"longitude\":\"2.3143739\",\"number_bedroom\":null,\"number_bathroom\":null,\"number_floor\":\"3\",\"square\":\"70\",\"price\":\"3490\",\"currency_id\":\"4\",\"commission\":null,\"deposit\":null,\"never_expired\":\"1\",\"bills_included\":\"0\",\"utilities\":null,\"furnished\":\"1\",\"pets_allowed\":\"0\",\"smoking_allowed\":\"0\",\"online_view_tour\":\"0\",\"features\":[\"14\"],\"seo_meta\":{\"seo_title\":null,\"seo_description\":null,\"index\":\"index\"},\"seo_meta_image\":null,\"submitter\":\"apply\",\"language\":\"en_US\",\"status\":\"renting\",\"categories\":[\"7\"],\"unique_id\":null,\"project_id\":\"24\",\"author_id\":\"29\"}"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 60}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 514}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/PropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\PropertyController.php", "line": 151}], "start": **********.849399, "duration": 0.004200000000000001, "duration_str": "4.2ms", "memory": 0, "memory_str": null, "filename": "AuditHandlerListener.php:60", "source": {"index": 11, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 60}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fsrc%2FListeners%2FAuditHandlerListener.php:60", "ajax": false, "filename": "AuditHandlerListener.php", "line": "60"}, "connection": "xmetr", "explain": null, "start_percent": 38.955, "width_percent": 11.032}, {"sql": "select * from `re_property_features` where `re_property_features`.`property_id` = 1171", "type": "query", "params": [], "bindings": [1171], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/PropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\PropertyController.php", "line": 157}, {"index": 18, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/PropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\PropertyController.php", "line": 131}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.8568559, "duration": 0.00409, "duration_str": "4.09ms", "memory": 0, "memory_str": null, "filename": "PropertyController.php:157", "source": {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/PropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\PropertyController.php", "line": 157}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FPropertyController.php:157", "ajax": false, "filename": "PropertyController.php", "line": "157"}, "connection": "xmetr", "explain": null, "start_percent": 49.987, "width_percent": 10.743}, {"sql": "update `re_properties` set `suitable_for` = '[]', `re_properties`.`updated_at` = '2025-07-26 16:26:43' where `id` = 1171", "type": "query", "params": [], "bindings": ["[]", "2025-07-26 16:26:43", 1171], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/PropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\PropertyController.php", "line": 161}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/PropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\PropertyController.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.866914, "duration": 0.004030000000000001, "duration_str": "4.03ms", "memory": 0, "memory_str": null, "filename": "PropertyController.php:161", "source": {"index": 14, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/PropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\PropertyController.php", "line": 161}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FPropertyController.php:161", "ajax": false, "filename": "PropertyController.php", "line": "161"}, "connection": "xmetr", "explain": null, "start_percent": 60.73, "width_percent": 10.586}, {"sql": "delete from `re_facilities_distances` where `re_facilities_distances`.`reference_id` = 1171 and `reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property'", "type": "query", "params": [], "bindings": [1171, "Xmetr\\RealEstate\\Models\\Property"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/real-estate/src/Services/SaveFacilitiesService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Services\\SaveFacilitiesService.php", "line": 12}, {"index": 13, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/PropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\PropertyController.php", "line": 163}, {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/PropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\PropertyController.php", "line": 131}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.877873, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "SaveFacilitiesService.php:12", "source": {"index": 12, "namespace": null, "name": "platform/plugins/real-estate/src/Services/SaveFacilitiesService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Services\\SaveFacilitiesService.php", "line": 12}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FServices%2FSaveFacilitiesService.php:12", "ajax": false, "filename": "SaveFacilitiesService.php", "line": "12"}, "connection": "xmetr", "explain": null, "start_percent": 71.316, "width_percent": 2.075}, {"sql": "select * from `re_property_categories` where `re_property_categories`.`property_id` = 1171", "type": "query", "params": [], "bindings": [1171], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Services/StorePropertyCategoryService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Services\\StorePropertyCategoryService.php", "line": 16}, {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/PropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\PropertyController.php", "line": 164}, {"index": 19, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/PropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\PropertyController.php", "line": 131}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.884049, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "StorePropertyCategoryService.php:16", "source": {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Services/StorePropertyCategoryService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Services\\StorePropertyCategoryService.php", "line": 16}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FServices%2FStorePropertyCategoryService.php:16", "ajax": false, "filename": "StorePropertyCategoryService.php", "line": "16"}, "connection": "xmetr", "explain": null, "start_percent": 73.391, "width_percent": 2.837}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'status' and `reference_id` = 1171 and `reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property') limit 1", "type": "query", "params": [], "bindings": ["status", 1171, "Xmetr\\RealEstate\\Models\\Property"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 147}, {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 63}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Traits/Forms/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Traits\\Forms\\HasMetadata.php", "line": 71}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 498}], "start": **********.890168, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 76.228, "width_percent": 1.996}, {"sql": "delete from `meta_boxes` where (`meta_key` = 'commission' and `reference_id` = 1171 and `reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property')", "type": "query", "params": [], "bindings": ["commission", 1171, "Xmetr\\RealEstate\\Models\\Property"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 201}, {"index": 14, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 65}, {"index": 15, "namespace": null, "name": "platform/themes/xmetr/functions/functions.php", "file": "D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\functions\\functions.php", "line": 903}, {"index": 19, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 500}], "start": **********.898145, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "MetaBox.php:201", "source": {"index": 12, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 201}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FSupports%2FMetaBox.php:201", "ajax": false, "filename": "MetaBox.php", "line": "201"}, "connection": "xmetr", "explain": null, "start_percent": 78.224, "width_percent": 2.653}, {"sql": "delete from `meta_boxes` where (`meta_key` = 'deposit' and `reference_id` = 1171 and `reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property')", "type": "query", "params": [], "bindings": ["deposit", 1171, "Xmetr\\RealEstate\\Models\\Property"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 201}, {"index": 14, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 65}, {"index": 15, "namespace": null, "name": "platform/themes/xmetr/functions/functions.php", "file": "D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\functions\\functions.php", "line": 904}, {"index": 19, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 500}], "start": **********.9034638, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "MetaBox.php:201", "source": {"index": 12, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 201}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FSupports%2FMetaBox.php:201", "ajax": false, "filename": "MetaBox.php", "line": "201"}, "connection": "xmetr", "explain": null, "start_percent": 80.877, "width_percent": 2.443}, {"sql": "update `re_properties` set `type` = 'rent', `period` = 'month', `re_properties`.`updated_at` = '2025-07-26 16:26:43' where `id` = 1171", "type": "query", "params": [], "bindings": [{"value": "rent", "label": "Rent"}, {"value": "month", "label": "Monthly"}, "2025-07-26 16:26:43", 1171], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "platform/themes/xmetr/functions/functions.php", "file": "D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\functions\\functions.php", "line": 907}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 500}, {"index": 25, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/PropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\PropertyController.php", "line": 131}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.91479, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "functions.php:907", "source": {"index": 18, "namespace": null, "name": "platform/themes/xmetr/functions/functions.php", "file": "D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\functions\\functions.php", "line": 907}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Ffunctions%2Ffunctions.php:907", "ajax": false, "filename": "functions.php", "line": "907"}, "connection": "xmetr", "explain": null, "start_percent": 83.32, "width_percent": 2.889}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'seo_meta' and `reference_id` = 1171 and `reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property') limit 1", "type": "query", "params": [], "bindings": ["seo_meta", 1171, "Xmetr\\RealEstate\\Models\\Property"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 147}, {"index": 20, "namespace": null, "name": "platform/packages/seo-helper/src/SeoHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\seo-helper\\src\\SeoHelper.php", "line": 171}, {"index": 22, "namespace": null, "name": "platform/packages/seo-helper/src/Listeners/UpdatedContentListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\seo-helper\\src\\Listeners\\UpdatedContentListener.php", "line": 15}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}], "start": **********.922385, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 86.21, "width_percent": 1.445}, {"sql": "select * from `slugs` where (`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property' and `reference_id` = 1171) limit 1", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property", 1171], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/packages/slug/src/Listeners/UpdatedContentListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\slug\\src\\Listeners\\UpdatedContentListener.php", "line": 49}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 23, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 514}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 505}], "start": **********.928775, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 87.654, "width_percent": 1.865}, {"sql": "insert into `audit_histories` (`user_agent`, `ip_address`, `module`, `action`, `user_id`, `reference_user`, `reference_id`, `reference_name`, `type`, `created_at`, `updated_at`, `request`) values ('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '127.0.0.1', 'property', 'updated', 1, 1, 1171, 'Long-term rent of a 3-room apartment 70m² in Paris, France', 'primary', '2025-07-26 16:26:43', '2025-07-26 16:26:43', '{\\\"name\\\":\\\"Long-term rent of a 3-room apartment 70m\\\\u00b2 in Paris, France\\\",\\\"model\\\":\\\"Xmetr\\\\\\\\RealEstate\\\\\\\\Models\\\\\\\\Property\\\",\\\"slug\\\":\\\"long-term-rent-of-a-3-room-apartment-70m2-in-paris-france-2\\\",\\\"slug_id\\\":\\\"2886\\\",\\\"is_slug_editable\\\":\\\"1\\\",\\\"description\\\":null,\\\"original_description\\\":\\\"3\\\\u0445\\\\u043a\\\\u043e\\\\u043c\\\\u043d\\\\u0430\\\\u0442\\\\u043d\\\\u0430\\\\u044f \\\\u043a\\\\u0432\\\\u0430\\\\u0440\\\\u0442\\\\u0438\\\\u0440\\\\u0430\\\\r\\\\n70\\\\u043c2\\\\r\\\\n3\\\\u0439 \\\\u044d\\\\u0442\\\\u0430\\\\u0436 \\\\r\\\\nRue Salneuve 75017, \\\\u041f\\\\u0430\\\\u0440\\\\u0438\\\\u0436 \\\\r\\\\n3490\\\\u20ac \\\\/ \\\\u043c\\\\u0435\\\\u0441\\\\u044f\\\\u0446 \\\\r\\\\n\\\\u0421\\\\u0432\\\\u043e\\\\u0431\\\\u043e\\\\u0434\\\\u043d\\\\u0430 \\\\u0441 30\\\\/06\\\",\\\"is_featured\\\":\\\"0\\\",\\\"content\\\":\\\"<p>3 room apartment<br>70m2<br>3rd floor<br>Rue Salneuve 75017, Paris<br>3490\\\\u20ac \\\\/ month<br>Available from 30\\\\/06<\\\\/p>\\\",\\\"images\\\":[null,\\\".\\\\/photo-2025-06-19-205916.webp\\\",\\\".\\\\/photo-2025-06-19-205919.webp\\\",\\\".\\\\/photo-2025-06-19-205918.webp\\\",\\\".\\\\/photo-2025-06-19-205917.webp\\\",\\\".\\\\/photo-2025-06-19-205915.webp\\\",\\\".\\\\/photo-2025-06-19-205920.webp\\\"],\\\"video\\\":null,\\\"country_id\\\":\\\"27\\\",\\\"state_id\\\":null,\\\"city_id\\\":\\\"8189\\\",\\\"district_id\\\":null,\\\"location\\\":\\\"Rue Salneuve, 75017 \\\\u041f\\\\u0430\\\\u0440\\\\u0438\\\\u0436, \\\\u0424\\\\u0440\\\\u0430\\\\u043d\\\\u0446\\\\u0456\\\\u044f\\\",\\\"latitude\\\":\\\"48.8853784\\\",\\\"longitude\\\":\\\"2.3143739\\\",\\\"number_bedroom\\\":null,\\\"number_bathroom\\\":null,\\\"number_floor\\\":\\\"3\\\",\\\"square\\\":\\\"70\\\",\\\"price\\\":\\\"3490\\\",\\\"currency_id\\\":\\\"4\\\",\\\"commission\\\":null,\\\"deposit\\\":null,\\\"never_expired\\\":\\\"1\\\",\\\"bills_included\\\":\\\"0\\\",\\\"utilities\\\":null,\\\"furnished\\\":\\\"1\\\",\\\"pets_allowed\\\":\\\"0\\\",\\\"smoking_allowed\\\":\\\"0\\\",\\\"online_view_tour\\\":\\\"0\\\",\\\"features\\\":[\\\"14\\\"],\\\"seo_meta\\\":{\\\"seo_title\\\":null,\\\"seo_description\\\":null,\\\"index\\\":\\\"index\\\"},\\\"seo_meta_image\\\":null,\\\"submitter\\\":\\\"apply\\\",\\\"language\\\":\\\"en_US\\\",\\\"status\\\":\\\"renting\\\",\\\"categories\\\":[\\\"7\\\"],\\\"unique_id\\\":null,\\\"project_id\\\":\\\"24\\\",\\\"author_id\\\":\\\"29\\\"}')", "type": "query", "params": [], "bindings": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "127.0.0.1", "property", "updated", 1, 1, 1171, "Long-term rent of a 3-room apartment 70m² in Paris, France", "primary", "2025-07-26 16:26:43", "2025-07-26 16:26:43", "{\"name\":\"Long-term rent of a 3-room apartment 70m\\u00b2 in Paris, France\",\"model\":\"Xmetr\\\\RealEstate\\\\Models\\\\Property\",\"slug\":\"long-term-rent-of-a-3-room-apartment-70m2-in-paris-france-2\",\"slug_id\":\"2886\",\"is_slug_editable\":\"1\",\"description\":null,\"original_description\":\"3\\u0445\\u043a\\u043e\\u043c\\u043d\\u0430\\u0442\\u043d\\u0430\\u044f \\u043a\\u0432\\u0430\\u0440\\u0442\\u0438\\u0440\\u0430\\r\\n70\\u043c2\\r\\n3\\u0439 \\u044d\\u0442\\u0430\\u0436 \\r\\nRue Salneuve 75017, \\u041f\\u0430\\u0440\\u0438\\u0436 \\r\\n3490\\u20ac \\/ \\u043c\\u0435\\u0441\\u044f\\u0446 \\r\\n\\u0421\\u0432\\u043e\\u0431\\u043e\\u0434\\u043d\\u0430 \\u0441 30\\/06\",\"is_featured\":\"0\",\"content\":\"<p>3 room apartment<br>70m2<br>3rd floor<br>Rue Salneuve 75017, Paris<br>3490\\u20ac \\/ month<br>Available from 30\\/06<\\/p>\",\"images\":[null,\".\\/photo-2025-06-19-205916.webp\",\".\\/photo-2025-06-19-205919.webp\",\".\\/photo-2025-06-19-205918.webp\",\".\\/photo-2025-06-19-205917.webp\",\".\\/photo-2025-06-19-205915.webp\",\".\\/photo-2025-06-19-205920.webp\"],\"video\":null,\"country_id\":\"27\",\"state_id\":null,\"city_id\":\"8189\",\"district_id\":null,\"location\":\"Rue Salneuve, 75017 \\u041f\\u0430\\u0440\\u0438\\u0436, \\u0424\\u0440\\u0430\\u043d\\u0446\\u0456\\u044f\",\"latitude\":\"48.8853784\",\"longitude\":\"2.3143739\",\"number_bedroom\":null,\"number_bathroom\":null,\"number_floor\":\"3\",\"square\":\"70\",\"price\":\"3490\",\"currency_id\":\"4\",\"commission\":null,\"deposit\":null,\"never_expired\":\"1\",\"bills_included\":\"0\",\"utilities\":null,\"furnished\":\"1\",\"pets_allowed\":\"0\",\"smoking_allowed\":\"0\",\"online_view_tour\":\"0\",\"features\":[\"14\"],\"seo_meta\":{\"seo_title\":null,\"seo_description\":null,\"index\":\"index\"},\"seo_meta_image\":null,\"submitter\":\"apply\",\"language\":\"en_US\",\"status\":\"renting\",\"categories\":[\"7\"],\"unique_id\":null,\"project_id\":\"24\",\"author_id\":\"29\"}"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 60}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 514}, {"index": 23, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 505}], "start": **********.9382582, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "AuditHandlerListener.php:60", "source": {"index": 11, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 60}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fsrc%2FListeners%2FAuditHandlerListener.php:60", "ajax": false, "filename": "AuditHandlerListener.php", "line": "60"}, "connection": "xmetr", "explain": null, "start_percent": 89.519, "width_percent": 10.481}]}, "models": {"data": {"Xmetr\\RealEstate\\Models\\Currency": {"value": 43, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FCurrency.php:1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Facility": {"value": 23, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FFacility.php:1", "ajax": false, "filename": "Facility.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Feature": {"value": 22, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FFeature.php:1", "ajax": false, "filename": "Feature.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Project": {"value": 8, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FProject.php:1", "ajax": false, "filename": "Project.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Category": {"value": 3, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FCategory.php:1", "ajax": false, "filename": "Category.php", "line": "?"}}, "Xmetr\\Base\\Models\\MetaBox": {"value": 3, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FMetaBox.php:1", "ajax": false, "filename": "MetaBox.php", "line": "?"}}, "Xmetr\\Slug\\Models\\Slug": {"value": 2, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FModels%2FSlug.php:1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Xmetr\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Property": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FProperty.php:1", "ajax": false, "filename": "Property.php", "line": "?"}}, "Xmetr\\LanguageAdvanced\\Models\\TranslationResolver": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FModels%2FTranslationResolver.php:1", "ajax": false, "filename": "TranslationResolver.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Account": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FAccount.php:1", "ajax": false, "filename": "Account.php", "line": "?"}}, "Xmetr\\Location\\Models\\City": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FModels%2FCity.php:1", "ajax": false, "filename": "City.php", "line": "?"}}, "Xmetr\\Language\\Models\\Language": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php:1", "ajax": false, "filename": "Language.php", "line": "?"}}}, "count": 110, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "https://xmetr.gc/admin/real-estate/properties/edit/1171", "action_name": "property.edit.update", "controller_action": "Xmetr\\RealEstate\\Http\\Controllers\\PropertyController@update", "uri": "POST admin/real-estate/properties/edit/{property}", "controller": "Xmetr\\RealEstate\\Http\\Controllers\\PropertyController@update<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FPropertyController.php:120\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Xmetr\\RealEstate\\Http\\Controllers", "prefix": "admin/real-estate/properties", "file": "<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FPropertyController.php:120\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/real-estate/src/Http/Controllers/PropertyController.php:120-172</a>", "middleware": "web, core, auth", "duration": "2.13s", "peak_memory": "52MB", "response": "Redirect to https://xmetr.gc/admin/real-estate/properties/edit/1171", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1940854670 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1940854670\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-324519512 data-indent-pad=\"  \"><span class=sf-dump-note>array:44</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ytAjAn3gHkXOl5zsmwHgtRY8qTqW4NuHlaKuh6OF</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"58 characters\">Long-term rent of a 3-room apartment 70m&#178; in Paris, France</span>\"\n  \"<span class=sf-dump-key>model</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Xmetr\\RealEstate\\Models\\Property</span>\"\n  \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"59 characters\">long-term-rent-of-a-3-room-apartment-70m2-in-paris-france-2</span>\"\n  \"<span class=sf-dump-key>slug_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2886</span>\"\n  \"<span class=sf-dump-key>is_slug_editable</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>original_description</span>\" => \"\"\"\n    <span class=sf-dump-str title=\"98 characters\">3&#1093;&#1082;&#1086;&#1084;&#1085;&#1072;&#1090;&#1085;&#1072;&#1103; &#1082;&#1074;&#1072;&#1088;&#1090;&#1080;&#1088;&#1072;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"98 characters\">70&#1084;2<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"98 characters\">3&#1081; &#1101;&#1090;&#1072;&#1078; <span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"98 characters\">Rue Salneuve 75017, &#1055;&#1072;&#1088;&#1080;&#1078; <span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"98 characters\">3490&#8364; / &#1084;&#1077;&#1089;&#1103;&#1094; <span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"98 characters\">&#1057;&#1074;&#1086;&#1073;&#1086;&#1076;&#1085;&#1072; &#1089; 30/06</span>\n    \"\"\"\n  \"<span class=sf-dump-key>is_featured</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"114 characters\">&lt;p&gt;3 room apartment&lt;br&gt;70m2&lt;br&gt;3rd floor&lt;br&gt;Rue Salneuve 75017, Paris&lt;br&gt;3490&#8364; / month&lt;br&gt;Available from 30/06&lt;/p&gt;</span>\"\n  \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"30 characters\">./photo-2025-06-19-205916.webp</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"30 characters\">./photo-2025-06-19-205919.webp</span>\"\n    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"30 characters\">./photo-2025-06-19-205918.webp</span>\"\n    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"30 characters\">./photo-2025-06-19-205917.webp</span>\"\n    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"30 characters\">./photo-2025-06-19-205915.webp</span>\"\n    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"30 characters\">./photo-2025-06-19-205920.webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>video</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>country_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">27</span>\"\n  \"<span class=sf-dump-key>state_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>city_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8189</span>\"\n  \"<span class=sf-dump-key>district_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>location</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Rue Salneuve, 75017 &#1055;&#1072;&#1088;&#1080;&#1078;, &#1060;&#1088;&#1072;&#1085;&#1094;&#1110;&#1103;</span>\"\n  \"<span class=sf-dump-key>latitude</span>\" => \"<span class=sf-dump-str title=\"10 characters\">48.8853784</span>\"\n  \"<span class=sf-dump-key>longitude</span>\" => \"<span class=sf-dump-str title=\"9 characters\">2.3143739</span>\"\n  \"<span class=sf-dump-key>number_bedroom</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>number_bathroom</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>number_floor</span>\" => \"<span class=sf-dump-str>3</span>\"\n  \"<span class=sf-dump-key>square</span>\" => \"<span class=sf-dump-str title=\"2 characters\">70</span>\"\n  \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">3490</span>\"\n  \"<span class=sf-dump-key>currency_id</span>\" => \"<span class=sf-dump-str>4</span>\"\n  \"<span class=sf-dump-key>commission</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>deposit</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>never_expired</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>bills_included</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>utilities</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>furnished</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>pets_allowed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>smoking_allowed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>online_view_tour</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>features</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">14</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>seo_meta</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>seo_title</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>seo_description</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>index</span>\" => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>seo_meta_image</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>submitter</span>\" => \"<span class=sf-dump-str title=\"5 characters\">apply</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"5 characters\">en_US</span>\"\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">renting</span>\"\n  \"<span class=sf-dump-key>categories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>unique_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>project_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">24</span>\"\n  \"<span class=sf-dump-key>author_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">29</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-324519512\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1733619674 data-indent-pad=\"  \"><span class=sf-dump-note>array:21</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1858</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">https://xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">https://xmetr.gc/admin/real-estate/properties/edit/1171</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1709 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjE5eVFoNXI3S3dCZTVpWVUvb1ZlTkE9PSIsInZhbHVlIjoiVFFEOHg1MHRNUCtIQndDL1FUdThDSVJxSzFQTTlRRWNjUGJtalZRTmpvRFQrQ1UybGVrWnR6dUN0ZU16VVJXRWg0UVg5cFdaQmlmL212RUdPSjJLcUgray8wa3JyeFU3LzUrRUs3by9jVXM1cCt0MDZCTXZjczI2RTRNR0xqeTBJT1dpcWNNQTdEeW8wYkZ1S3lUc1pZSVFCd2FiN2EvdTRmZGdiU2FOYVREVUtPdGw5SG1xOUZHVU5Db0Zwa1lvVTRXRDFPOXBhNnZOQ3hkWjJjU2JtaDRQRU9WYXRwczhPTmM1Wit2aThBcz0iLCJtYWMiOiI0NThmZWI3NDI2NThmMTIxOWRlN2JiMjljY2Y3MjQzMDUzNmQyYjA5NzllZTg4YmY2ZTc5MGExYjEzODJhMGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.2064844405.1743885089; cookie_for_consent=1; _hjSessionUser_6417422=eyJpZCI6IjhkMzA2ODc1LTcwNjctNTU3Yi1hNDljLTdhNmEyNTdkM2QxOCIsImNyZWF0ZWQiOjE3NDgzODAwMDM5NTcsImV4aXN0aW5nIjp0cnVlfQ==; wishlist=657; project_wishlist=25; _hjSession_6417422=eyJpZCI6ImQzMGMxNDdkLWIzYjAtNGUzMi1hMDU3LWNjYWIzYWVmODhjNyIsImMiOjE3NTM1NDU3MDQ3MzQsInMiOjEsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjowfQ==; _ga_KQ6X76DET4=GS2.1.s1753545705$o107$g1$t1753547136$j60$l0$h0; XSRF-TOKEN=eyJpdiI6InZhYTk2c09zVndSWmtmNDM1ZVJsTkE9PSIsInZhbHVlIjoiV2h1bjdoQ2p6L2pycDBObWZSWWY1eEk0WG1VeVFYNnNjUlJKd0NYR3A0bXZqcnBGUWt4cDNPVzdISm9RcWhXSlZiRUJ6SGthT2lzWnVhOVFVZlBxdGp3VDJreXVLSmhQVGVQbGcyeUFubFpraFZsem5UVXFPYm42RzNFU0xrQlYiLCJtYWMiOiI4NzdhNTIzMDNmMThmZTc4NzhjZjA4NzY3MzUyNTllMzgyYTgyYWYxOGNhYjJhODM2ZmUzMjU5ZWJlZWJmMzQ0IiwidGFnIjoiIn0%3D; xmetr_session=eyJpdiI6IkEwblB0OFdEVjd3eE5LeVhFWS9penc9PSIsInZhbHVlIjoiK0Z0TDJIV3p1R2tqdStVbDFwV2grMkJlbW5rV0pycktMajBrVFE3cmFKM2tONmNxaVMvVEZ4K0tEWkE1VFZKZ1NmRE5nZXYzd2VIVlRGTm1tVUFWc3BKSjBSakxTL0d5UzNGbDMzUGRFQkVTdEZjVENhU2ZzNitsMDdtZGdMQnUiLCJtYWMiOiIzZjc3NzE3NzQxZTdjMGUxZjBmYjk4NzEzZjhiOTY3MDRhYmVjNTAwNWZjNWNkN2I0N2IxODRkMDE2N2IxMmM3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1733619674\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2116412070 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|1uQwj4bI0uiYsJi0APplQif6Zji5Zq40vBOmafc1oj6NYK3vu89uqM3GLC7O|$2y$04$Astns3SXblBceX03zRicBuVKoyIRRFaOY1GuvREgnRYhHEelfNbdu</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>_hjSessionUser_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>project_wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_hjSession_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_KQ6X76DET4</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ytAjAn3gHkXOl5zsmwHgtRY8qTqW4NuHlaKuh6OF</span>\"\n  \"<span class=sf-dump-key>xmetr_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2vBKbIuUvKuaQ8TwrzXlARgwysIuoj6o6VDmQW3H</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2116412070\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1289994450 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 26 Jul 2025 16:26:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">https://xmetr.gc/admin/real-estate/properties/edit/1171</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1289994450\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1749678531 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ytAjAn3gHkXOl5zsmwHgtRY8qTqW4NuHlaKuh6OF</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">https://xmetr.gc/admin/real-estate/properties/edit/1171</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">success_msg</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>viewed_property</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1172</span> => <span class=sf-dump-num>1753547020</span>\n  </samp>]\n  \"<span class=sf-dump-key>viewed_property_daily</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1172</span> => <span class=sf-dump-num>1753547020</span>\n  </samp>]\n  \"<span class=sf-dump-key>success_msg</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Updated successfully</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1749678531\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "https://xmetr.gc/admin/real-estate/properties/edit/1171", "action_name": "property.edit.update", "controller_action": "Xmetr\\RealEstate\\Http\\Controllers\\PropertyController@update"}, "badge": "302 Found"}}